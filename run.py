#!/usr/bin/env python3
"""
Product Matcher Web Application Launcher

This script provides an easy way to start the Product Matcher web application
with proper error handling and user-friendly messages.
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    # Map package names to their import names
    required_packages = {
        'flask': 'flask',
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'scikit-learn': 'sklearn',  # scikit-learn imports as sklearn
        'rapidfuzz': 'rapidfuzz',
        'numpy': 'numpy',
        'tqdm': 'tqdm',
        'langdetect': 'langdetect'
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 To install missing packages, run:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['uploads', 'results', 'templates', 'static']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Created necessary directories")

def start_application():
    """Start the Flask application"""
    try:
        print("🚀 Starting Product Matcher Web Application...")
        print("📍 Application will be available at: http://localhost:5000")
        print("🔄 Starting server...")
        
        # Import and run the Flask app
        from app import app
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 Opened web browser automatically")
            except:
                print("💡 Please open your browser and navigate to: http://localhost:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask application
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("💡 Please check the error message above and try again")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 PRODUCT MATCHER WEB APPLICATION")
    print("=" * 60)
    print()
    
    # Check Python version
    print("🐍 Checking Python version...")
    if not check_python_version():
        input("Press Enter to exit...")
        return
    print("✅ Python version is compatible")
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        print("\n💡 Install dependencies with: pip install -r requirements.txt")
        input("Press Enter to exit...")
        return
    print("✅ All dependencies are installed")
    
    # Create directories
    print("📁 Setting up directories...")
    create_directories()
    
    # Start application
    print("\n" + "=" * 60)
    start_application()

if __name__ == "__main__":
    main()
