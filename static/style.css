/* Additional custom styles for the Product Matcher */

.drag-drop-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drag-drop-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.drag-drop-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.file-info {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.progress-container {
    margin: 1rem 0;
}

.match-score {
    font-weight: bold;
}

.match-score.high {
    color: #28a745;
}

.match-score.medium {
    color: #ffc107;
}

.match-score.low {
    color: #dc3545;
}

.column-preview {
    max-height: 150px;
    overflow-y: auto;
    font-size: 0.875rem;
}

.sheet-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 1rem;
    border-radius: 0.375rem 0.375rem 0 0;
}

.language-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.language-indicator i {
    font-size: 1.2rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.result-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    text-align: center;
    transition: transform 0.2s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.result-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.result-number.success {
    color: #28a745;
}

.result-number.warning {
    color: #ffc107;
}

.result-number.info {
    color: #17a2b8;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    text-align: center;
    max-width: 400px;
}

.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 1rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
}

.success-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 1rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
}

.info-message {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 1rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .step-indicator {
        flex-direction: column;
        gap: 1rem;
    }
    
    .step {
        justify-content: center;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .column-mapping {
        margin: 0.5rem 0;
    }
}

/* Animation for step transitions */
.card {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar for preview tables */
.preview-table::-webkit-scrollbar {
    width: 6px;
}

.preview-table::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.preview-table::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.preview-table::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
