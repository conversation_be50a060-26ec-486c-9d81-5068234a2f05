import re
import math
import argparse
import unicodedata
from tqdm.auto import tqdm
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any, List
import os
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.neighbors import NearestNeighbors
from rapidfuzz import fuzz

# Web application imports
from flask import Flask, render_template, request, jsonify, send_file, session
import uuid
from werkzeug.utils import secure_filename
import tempfile
import json
from datetime import datetime
import langdetect

# Optional embeddings (loaded lazily if enabled)
EMBED_MODEL = None

# ----------------------------
# Arabic normalization
# ----------------------------

ARABIC_DIACRITICS = re.compile(r"[\u0610-\u061A\u064B-\u065F\u06D6-\u06ED]")
TATWEEL = "\u0640"
ARABIC_DIGITS = str.maketrans("٠١٢٣٤٥٦٧٨٩", "0123456789")
PERSIAN_DIGITS = str.maketrans("۰۱۲۳۴۵۶۷۸۹", "0123456789")
PUNCT_PATTERN = re.compile(r"[^\w\s.]+", flags=re.UNICODE)  # Preserve decimal points

def normalize_arabic(text: str) -> str:
    if not isinstance(text, str):
        return ""
    text = text.strip()
    text = unicodedata.normalize("NFKC", text)
    text = ARABIC_DIACRITICS.sub("", text)
    text = text.replace(TATWEEL, "")
    text = re.sub("[إأآٱا]", "ا", text)
    text = text.replace("ة", "ه")
    text = text.replace("ى", "ي").replace("ئ", "ي").replace("ؤ", "و")
    text = text.replace("×", "x").replace("✕", "x").replace("*", "x")
    text = text.translate(ARABIC_DIGITS).translate(PERSIAN_DIGITS)
    text = text.lower()
    text = re.sub(r"[/|+_,\-]+", " ", text)
    text = PUNCT_PATTERN.sub(" ", text)
    text = re.sub(r"\s+", " ", text).strip()
    return text

# ----------------------------
# Unit/pack extraction
# ----------------------------

UNIT_PATTERNS = [
    (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:كيلو?جرام|كجم|كلغ|كغ|كيلو|ك|kg)\b"), 1000.0, "g"),  # Added ك for kg
    (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:جرام|غ|جم|g)\b"), 1.0, "g"),
    (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:ليتر|لتر|ل|ltr|liter|litre|lt|l)\b"), 1000.0, "ml"),
    (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:مل|مللي|ml)\b"), 1.0, "ml"),
    (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:cl)\b"), 10.0, "ml"),
]

PACK_PATTERNS = [
    re.compile(r"\b(\d+)\s*x\s*(\d+(?:[.,]\d+)?)\s*(?:مل|ml|جرام|غ|جم|g|kg|كجم|كيلو?جرام|ل|لتر|ليتر|l|ltr)?\b"),
    re.compile(r"\b(\d+)\s*x\b"),
    re.compile(r"\bعبوه?\s*(\d+)\b"),
    re.compile(r"\bعلبه?\s*(\d+)\b"),
    re.compile(r"\bعدد\s*(\d+)\b"),
]

TEABAG_PAT = re.compile(r"\b(\d+)\s*(?:فتله|فتلة|تي\s*با[جج]|tea\s*bags?)\b")
DIAPER_PAT = re.compile(r"(?:حفاضه|حفاضة|حفاض)(\d+)(?:x|X)?")  # Pattern for "حفاضة32X" format
CUBE_PAT = re.compile(r"\b(\d+)\s*(?:مكعب|مكعبات|cubes?)\b")  # For bouillon cubes
TOWEL_PAT = re.compile(r"\b(\d+)\s*(?:فوطه|فوطة|فوط)\b")  # For sanitary towels
TISSUE_LAYER_PAT = re.compile(r"\b(\d+)\s*(?:طبقه|طبقة|طبقات|layer|layers)\b")  # For tissue layers
TISSUE_COUNT_PAT = re.compile(r"\b(\d+)\s*(?:منديل|مناديل|tissue|tissues)\b")  # For tissue count
ROLL_PAT = re.compile(r"\b(\d+)\s*(?:رول|roll|rolls)\b")  # For tissue rolls
UNITCOUNT_PAT = re.compile(r"\b(\d+)\s*(?:كيس|اكياس|صابونه|صابونة|قطعه|قطعة|ظرف|علبه|علبة|كانز|قالب|لوح|حبه|حبة|بكرة|رول|زجاجة|زجاجه|قاروره|قارورة|قنينه|قنينة|زجاج)\b")
DIAPER_SIZE_PAT = re.compile(r"\bمقاس\s*(\d+)\b")

def to_float(num: Any) -> float:
    try:
        return float(str(num).replace(",", "."))
    except Exception:
        return float("nan")

def extract_pack_info(text: str) -> Dict[str, Any]:
    result = {
        "pack_count": np.nan,
        "item_size_value": np.nan,
        "item_size_unit": None,
        "teabags_count": np.nan,
        "diapers_count": np.nan,
        "diaper_size": np.nan,
        "cube_count": np.nan,  # For bouillon cubes
        "towel_count": np.nan,  # For sanitary towels
        "tissue_layers": np.nan,  # For tissue layers
        "tissue_count": np.nan,  # For tissue count
        "roll_count": np.nan,  # For tissue rolls
    }
    if not text:
        return result

    for pat in PACK_PATTERNS:
        m = pat.search(text)
        if m:
            if "x" in pat.pattern and len(m.groups()) >= 2:
                result["pack_count"] = to_float(m.group(1))
                result["item_size_value"] = to_float(m.group(2))
                tail = text[m.end(): m.end() + 8]
                unit_match = re.search(r"(ml|مل|g|جم|جرام|غ|kg|كجم|ل|لتر|ليتر|ltr|l)", tail)
                if unit_match:
                    result["item_size_unit"] = unit_match.group(1)
            else:
                num_str = next((g for g in m.groups() if g is not None), None)
                if num_str:
                    result["pack_count"] = to_float(num_str)
            break

    m = TEABAG_PAT.search(text)
    if m:
        result["teabags_count"] = to_float(m.group(1))

    # Extract diaper count with specific pattern for "حفاضة32X" format
    m = DIAPER_PAT.search(text)
    if m:
        result["diapers_count"] = to_float(m.group(1))
    else:
        # Fallback: look for standalone numbers before "حفاضة"
        fallback_pat = re.compile(r"(\d+)\s*(?:حفاضه|حفاضة|حفاضات)")
        m = fallback_pat.search(text)
        if m:
            result["diapers_count"] = to_float(m.group(1))

    # Extract cube count for bouillon cubes
    m = CUBE_PAT.search(text)
    if m:
        result["cube_count"] = to_float(m.group(1))

    # Extract towel count for sanitary towels
    m = TOWEL_PAT.search(text)
    if m:
        result["towel_count"] = to_float(m.group(1))

    # Extract tissue layers
    m = TISSUE_LAYER_PAT.search(text)
    if m:
        result["tissue_layers"] = to_float(m.group(1))

    # Extract tissue count
    m = TISSUE_COUNT_PAT.search(text)
    if m:
        result["tissue_count"] = to_float(m.group(1))

    # Extract roll count for tissues
    m = ROLL_PAT.search(text)
    if m:
        result["roll_count"] = to_float(m.group(1))

    m = UNITCOUNT_PAT.search(text)
    if m and (math.isnan(result["pack_count"]) or result["pack_count"] < 2):
        val = to_float(m.group(1))
        if val >= 2:
            result["pack_count"] = val

    m = DIAPER_SIZE_PAT.search(text)
    if m:
        result["diaper_size"] = to_float(m.group(1))

    return result

def extract_main_unit(text: str) -> Dict[str, Any]:
    best_g = np.nan
    best_ml = np.nan
    if not text:
        return {"net_weight_g": np.nan, "volume_ml": np.nan}
    for pat, factor, target in UNIT_PATTERNS:
        for m in pat.finditer(text):
            value = to_float(m.group(1)) * factor
            if target == "g":
                if math.isnan(best_g) or value > best_g:
                    best_g = value
            else:
                if math.isnan(best_ml) or value > best_ml:
                    best_ml = value
    return {"net_weight_g": best_g, "volume_ml": best_ml}

# ----------------------------
# Noise + vocab
# ----------------------------

ARABIC_NOISE_WORDS = {
    "عرض","عرض خاص","مجانا","هديه","هدية","مجاني","جديد","خصم","بوكس","علبه","علبة","علب","عبوه","عبوة","عبوات",
    "سعر","مسعر","الجديد","المحسن","حجم","كبير","صغير","متوسط","اكسترا","زيادة","اكسبرس","اكسبريس",
    "كو","شركة","شركه","براند","ماركه","ماركة","اصلي","اصلية","اصليه","جنيه","جنيها","ج","التوفير","اقتصادي",
    "تقريبا","تقريباً","صافي","غيار","شرنك","شرينك","شريط","باك","باكت","+"
}

FLAVOR_KEYWORDS = {
    "مانجو","برتقال","تفاح","اناناس","فراوله","فراولة","جوافه","جوافة","كيوي","ليمون","خوخ","توت","توت بري",
    "رمان","مشمش","شمام","كنتالوب","عنب","عنب احمر","عنب ابيض","تفاح اخضر","تفاح احمر","موز","ليمون بالنعناع",
    "يوسفي","يوسف افندي","مندرين","جريب فروت","جوافه مع مانجو","ليمون مع نعناع","تفاح بالقرفه","تفاح بالقرفة",
    "جوز هند","جوزهند","pina colada","بينا كولادا","blackcurrant","كشمش اسود","كشمش الأسود","cranberry","كراانبيري",
    "توت ازرق","بلوبيري","فراولة مع موز","كوكتيل","خوخ","كولا","صودا ليمون","ليمون صودا",
    "تمر","تمر هندي","حبحب","بطيخ","شمام مع بطيخ","كريز","كرز","كيوي",
    "مانجو بالبرتقال","كوكتيل فواكه","بندق","كراميل","لوز","مكسرات","دارك","شوكولاته","شوكولاتة","مارشميلو",
    "نعناع","اوريو","لوتس","فانيليا","موكا","سبايسي","قهوة","بستاشيو","فستق","زنجبيل","زعرور","قشطة",
    "ينسون","كركديه","بابونج","قرفه","قرفة","كمون","كراويه","كراوية","حبهان","هيل","زعتر","ياسمين",
    # Additional flavors and scents for better detection
    "ليمونادا","ميكس توت","تين","مشمش","فراولة","لافندر","لاڤندر","نسيم اللافندر","ليمون اخضر","داوني","فل",
    "اصلي","اورينتال","بلاك","روز","عود","حار","مكسيكيه","توت","جوز الهند","خالي الدسم","كامل الدسم","دارك"
}
VARIANT_KEYWORDS = {
    "مفتتة","مفتت","قطع","زيت","مياه","ساده","سادة","مملحه","مملحة","خالية الدسم","كاملة الدسم",
    "مطحون","حبوب كامله","حبوب كاملة","حبوب","نباتي","حيواني","عسل نحل","عسل اسود","بيض احمر","بيض ابيض",
    "بلدي","مجمد","ارز ابيض","ارز بسمتي","ارز مصري","اقلام","صوابع","شعرية","اسباجيتي","مركز","طبيعي","نكتار",
    "سادة","محشو","مغلف بالشوكولاته","مغلف بالشوكولاتة","رومانسية","لايت","دايت","diet","زيرو","zero","بدون سكر","خالي من السكر","سكر دايت","سوجر فري","sugar free","sugarfree",
    "بلس","اكسترا","اقتصادي","جامبو","عائلي","سوبر","لارج","large","ميني","mini","صغير","كبير","متوسط","ناعم","خشن",
    "احمر","ابيض","اسود","اخضر","ازرق","اصفر","موف","بنفسجي","وردي","حار","بارد","ساخن","مثلج",
    "ملح","مالح","جبن","جبنة","بالجبن","بالجبنه",
    "يدوي","اوتوماتيك","automatic","manual","بقوه الانزيمات","بالانزيمات","للابيض","للالوان","للبيض","للملون",
    "اوريجنال","original","كلاسيك","classic","تقليدي","عادي","بيور","pure","طبيعي","natural","ريتش","rich","كوول","كول",
    "محشو بالتمر","بالتمر","بالزبده","بالزبدة","بالشوكولاته","بالشوكولاتة","بالكريمه","بالكريمة",
    "فتله","فتلة","اكياس","ظرف","اكياس شاي","تي باج","teabag","tea bag","بانتس","pants"
}
SCENT_KEYWORDS = {
    "لافندر","lavender","ياسمين","ورد","نسيم البحر","فواكه","ليمون","نعناع","mint","صنوبر","تفاح اخضر","ازهار الربيع","زهور الربيع",
    "بخور","مسك","صابون طبيعي","زهرة اللوتس","زهرة الكرز","فانيليا","vanilla","قطن ناعم","ازهار بيضاء","نسيم الغابه","نسيم الغابة",
    "زهرة الاوركيد","زيت الزيتون","صابون مغربي","العنبر","الكهرمان","ندي الربيع","روز","rose","روز نواعم الزهور","نسيم الربيع","نسيم الشرق","نسيم العطور",
    "نسيم اللافندر","برائحه الليمون","برائحة الليمون","رائحه","رائحة"
}
PACKAGING_KEYWORDS = {
    "كيس","عبوة","زجاجه","زجاجة","كرتونة","علبة","علبه","كانز","صفيحه","صفيحة","ظرف","رول","سطل","جردل","انبوبه","أنبوبه","انبوبة","برطمان",
    "قابلة للغلق",
}

def build_features(raw_name: str) -> Dict[str, Any]:
    clean = normalize_arabic(raw_name or "")
    pack = extract_pack_info(clean)
    unit = extract_main_unit(clean)

    # Create clean_core_with_scents (before removing scent words) for scent conflict detection
    clean_core_with_scents = re.sub(r"\b(\d+(?:[.,]\d+)?)\b", " ", clean)
    clean_core_with_scents = re.sub(r"\b(مل|ml|جرام|جم|غ|g|kg|كجم|كيلو?جرام|ل|لتر|ليتر|ltr|l)\b", " ", clean_core_with_scents)
    clean_core_with_scents = re.sub(r"\bx\b", " ", clean_core_with_scents)
    clean_core_with_scents = re.sub(r"\s+", " ", clean_core_with_scents).strip()

    # Create regular clean_core (with noise words removed)
    clean_core = clean_core_with_scents
    tokens = [t for t in clean_core.split() if t not in ARABIC_NOISE_WORDS]
    clean_core = " ".join(tokens)
    clean_core = re.sub(r"\s+", " ", clean_core).strip()

    return {
        "original_text": raw_name,  # Store original text for scent conflict detection
        "clean_name": clean,
        "clean_core": clean_core,
        "clean_core_with_scents": clean_core_with_scents,  # For scent detection
        **pack,
        **unit,
    }

def _norm_kw_set(words: set) -> set:
    return {normalize_arabic(w) for w in words}

FLAVOR_SET = _norm_kw_set(FLAVOR_KEYWORDS)
VARIANT_SET = _norm_kw_set(VARIANT_KEYWORDS)
SCENT_SET = _norm_kw_set(SCENT_KEYWORDS)
PACKAGING_SET = _norm_kw_set(PACKAGING_KEYWORDS)

# Critical variant tags that imply a fundamentally different product line (e.g., diet/zero)
CRITICAL_VARIANTS_RAW = {"دايت","diet","زيرو","zero","بدون سكر","خالي من السكر","سكر دايت","سوجر فري","sugar free","sugarfree"}
CRITICAL_VARIANTS = _norm_kw_set(CRITICAL_VARIANTS_RAW)

def extract_semantic_tags(clean_text: str) -> Dict[str, set]:
    def find_matches(vocab: set) -> set:
        found = set()
        for kw in vocab:
            if not kw:
                continue
            if kw in clean_text:
                found.add(kw)
        return found
    return {
        "flavors": find_matches(FLAVOR_SET),
        "variants": find_matches(VARIANT_SET),
        "scents": find_matches(SCENT_SET),
        "packs": find_matches(PACKAGING_SET),
    }

def semantic_bonus(a_core: str, b_core: str) -> float:
    a = extract_semantic_tags(a_core)
    b = extract_semantic_tags(b_core)
    bonus = 0.0

    def comp(sa: set, sb: set, w_match: float = 6.0, w_conflict: float = -10.0, cap: int = 2):
        nonlocal bonus
        if not sa or not sb:
            return
        inter = sa & sb
        if inter:
            # Scale bonus by number of overlapping tags (capped)
            bonus += w_match * min(len(inter), cap)
        else:
            bonus += w_conflict

    # Flavors and variants are highly indicative
    comp(a["flavors"], b["flavors"], w_match=7.0, w_conflict=-12.0, cap=2)
    comp(a["variants"], b["variants"], w_match=6.0, w_conflict=-9.0, cap=2)
    # Scents matter mostly for detergents and personal care
    comp(a["scents"], b["scents"], w_match=4.5, w_conflict=-7.0, cap=2)
    # Packaging has low importance
    comp(a["packs"], b["packs"], w_match=2.0, w_conflict=-3.0, cap=1)
    return bonus

# ----------------------------
# Category and product type checks
# ----------------------------

TEA_TOKENS = {"اعشاب","شاي","تي","tea"}
JUICE_TOKENS = {"عصير","نكتار","كوكتيل","بيور","pure","نكتار","مشروب"}
SODA_TOKENS = {"مشروب غازي","غازي","صودا","كولا","بيبسي","كوكاكولا","سبرايت"}
DETERGENT_TOKENS = {"مسحوق","جل","سائل","اوتوماتيك","يدوي","اطباق","ملابس","سيرفيس","مطهر","منظف"}
DIAPER_TOKENS = {"حفاض","حفاضة","حفاضات","بانتس","جامبو","pants"}
ROAST_TOKENS = {"فاتح","وسط","غامق"}
DISH_TOKENS = {"كفتة","كفته","برجر","بانية","بانيه","محشي","برياني","الأرز البريانى","شاورما"}

def contains_any(text: str, tokens: set) -> bool:
    return any(tok in text for tok in tokens)

def is_liquid_product(text: str) -> bool:
    return any(t in text for t in ["سائل","زيت","عصير","مشروب","جل","شامبو","صوص","خل","منظف","ماء"])

def unify_liquid_units(a: Dict[str, Any], b: Dict[str, Any], a_text: str, b_text: str) -> Tuple[float, float]:
    a_ml = a.get("volume_ml")
    b_ml = b.get("volume_ml")
    a_g = a.get("net_weight_g")
    b_g = b.get("net_weight_g")

    a_is_liq = is_liquid_product(a_text)
    b_is_liq = is_liquid_product(b_text)
    if a_is_liq or b_is_liq:
        if (isinstance(a_ml, float) and math.isnan(a_ml)) and (isinstance(a_g, float) and not math.isnan(a_g)):
            a_ml = a_g
        if (isinstance(b_ml, float) and math.isnan(b_ml)) and (isinstance(b_g, float) and not math.isnan(b_g)):
            b_ml = b_g
    return a_ml, b_ml

def hard_semantic_filter(a_row: Dict[str, Any], b_row: Dict[str, Any]) -> Optional[str]:
    a_core = a_row.get("clean_core","")
    b_core = b_row.get("clean_core","")

    a_tags = extract_semantic_tags(a_core)
    b_tags = extract_semantic_tags(b_core)

    # Critical variant mismatch (e.g., diet/zero) => reject if present on one side only
    a_has_crit = bool(a_tags["variants"] & CRITICAL_VARIANTS)
    b_has_crit = bool(b_tags["variants"] & CRITICAL_VARIANTS)
    if a_has_crit != b_has_crit:
        return "variant_critical_conflict"

    # First check for non-product items (phone cards, packages, etc.)
    non_product_indicators = [
        "ياقة", "ياقه", "كارت شحن", "باقة", "باقه", "شحن", "كارت", "بطاقة", "بطاقه",
        "رصيد", "خدمة", "خدمه", "عرض", "جنية", "جنيه"
    ]

    if contains_any(a_core, non_product_indicators) or contains_any(b_core, non_product_indicators):
        return "non_product_item"

    # Check for major product category mismatches
    major_categories = [
        (["سمنه", "سمن", "ghee", "butter"], "fats"),
        (["مكرونة", "مكرونه", "pasta", "noodles"], "pasta"),
        (["شاي", "tea"], "tea"),
        (["عصير", "juice"], "juice"),
        (["جل", "gel"], "gel"),
        (["مسحوق", "powder"], "powder"),
        (["ايس كريم", "ice cream"], "ice_cream"),
        (["كيك", "cake"], "cake"),
        (["بسكويت", "ويفر", "biscuit", "wafer"], "biscuits"),
        (["توست", "toast"], "toast"),
        (["شيبستيك", "كباب", "chips", "kabab"], "snacks"),
        (["منظف زجاج", "glass cleaner"], "glass_cleaner"),
        (["منظف ارضيات", "floor cleaner"], "floor_cleaner"),
        (["ماكينه حلاقه", "razor"], "razor"),
        (["صابون سائل", "liquid soap"], "liquid_soap"),
        (["صابون", "soap"], "soap"),
    ]

    a_category = None
    b_category = None

    for tokens, category in major_categories:
        if contains_any(a_core, tokens):
            a_category = category
        if contains_any(b_core, tokens):
            b_category = category

    # If both have categories and they're different, reject
    if a_category and b_category and a_category != b_category:
        return f"category_mismatch_{a_category}_vs_{b_category}"

    # Check for specific attribute conflicts
    attribute_conflicts = [
        (["خالي الدسم", "skimmed"], ["كامل الدسم", "full cream"]),  # Fat content
        (["حار", "spicy", "مكسيكيه"], ["مكس", "mix", "عادي"]),  # Spiciness vs regular
        (["دارك", "dark"], ["ساندوتش", "sandwich"]),  # Different biscuit types
        (["اصلي", "original"], ["لافندر", "lavender"]),  # Different scents
        (["اورينتال", "oriental"], ["بلاك", "black"]),  # Different scents
        (["سائل", "liquid"], ["يدوي", "manual", "powder"]),  # Liquid vs powder forms
        (["ويفر", "wafer"], ["توت", "berry"]),  # Different product types
    ]

    for attr1, attr2 in attribute_conflicts:
        if (contains_any(a_core, attr1) and contains_any(b_core, attr2)) or \
           (contains_any(a_core, attr2) and contains_any(b_core, attr1)):
            return "attribute_conflict"

    # Check for scent conflicts ONLY in cleaning products where scents are meaningful
    cleaning_indicators = ["منظف", "مسحوق", "جل", "سائل", "صابون", "برسيل", "اريال", "تايد", "اوكسي"]
    is_cleaning_product = (contains_any(a_core, cleaning_indicators) and contains_any(b_core, cleaning_indicators))

    if is_cleaning_product:
        # Use clean_core_with_scents to detect scent words before they're removed as noise
        a_scents = a_row.get("clean_core_with_scents", "")
        b_scents = b_row.get("clean_core_with_scents", "")

        # For cleaning products, اصلي vs لافندر is a scent conflict
        scent_conflicts = [
            (["اصلي"], ["لافندر", "lavender"]),  # Only for cleaning products
            (["اورينتال", "oriental"], ["بلاك", "black"]),
        ]

        for scent1, scent2 in scent_conflicts:
            if (contains_any(a_scents, scent1) and contains_any(b_scents, scent2)) or \
               (contains_any(a_scents, scent2) and contains_any(b_scents, scent1)):
                return "scent_conflict"

    # Beverages (tea/juice/soda): flavors must agree when present
    if (contains_any(a_core, TEA_TOKENS) or contains_any(b_core, TEA_TOKENS) or
        contains_any(a_core, JUICE_TOKENS) or contains_any(b_core, JUICE_TOKENS) or
        contains_any(a_core, SODA_TOKENS) or contains_any(b_core, SODA_TOKENS)):

        # If both have flavors, they must match
        if a_tags["flavors"] and b_tags["flavors"] and not (a_tags["flavors"] & b_tags["flavors"]):
            return "flavor_conflict"

    # Detergents/home care: scents must agree when present
    if contains_any(a_core, DETERGENT_TOKENS) or contains_any(b_core, DETERGENT_TOKENS):
        if a_tags["scents"] and b_tags["scents"] and not (a_tags["scents"] & b_tags["scents"]):
            return "scent_conflict"

    # Coffee: roast must agree when present
    if "قهوة" in a_core or "قهوة" in b_core or "بن" in a_core or "بن" in b_core:
        a_roast = {t for t in ROAST_TOKENS if t in a_core}
        b_roast = {t for t in ROAST_TOKENS if t in b_core}
        if a_roast and b_roast and not (a_roast & b_roast):
            return "coffee_roast_conflict"

    # Recipe/dish-specific mixes must align
    a_dish = {t for t in DISH_TOKENS if t in a_core}
    b_dish = {t for t in DISH_TOKENS if t in b_core}
    if a_dish and b_dish and not (a_dish & b_dish):
        return "recipe_conflict"

    a_size = a_row.get("diaper_size")
    b_size = b_row.get("diaper_size")
    if (contains_any(a_core, DIAPER_TOKENS) or contains_any(b_core, DIAPER_TOKENS)):
        if not (isinstance(a_size, float) and math.isnan(a_size)) and not (isinstance(b_size, float) and math.isnan(b_size)):
            if int(a_size) != int(b_size):
                return f"diaper_size_mismatch_{int(a_size)}_vs_{int(b_size)}"

    # Product type conflicts (different products that shouldn't match)
    product_type_conflicts = [
        (["ترمس", "thermos"], ["فول", "beans", "تدميس"]),  # Thermos vs beans
        (["جبنه", "جبنة", "cheese"], ["سمبوسك", "samosa"]),  # Cheese vs samosa
        (["سمنه", "سمن", "ghee"], ["مكرونة", "مكرونه", "pasta"]),  # Ghee vs pasta
        (["جل", "gel"], ["ابيض", "white", "مسحوق"]),  # Gel vs white powder
        (["ايس كريم", "ice cream"], ["مسحوق كيك", "cake mix"]),  # Ice cream vs cake mix
        (["شاي", "tea"], ["مكرونة", "pasta"]),  # Tea vs pasta (different categories)
        (["جل", "gel"], ["كجم", "powder"]),  # Gel (liquid) vs powder (solid)
    ]

    for type1_tokens, type2_tokens in product_type_conflicts:
        if (contains_any(a_core, type1_tokens) and contains_any(b_core, type2_tokens)) or \
           (contains_any(a_core, type2_tokens) and contains_any(b_core, type1_tokens)):
            return "product_type_conflict"

    return None

def category_penalty(a_row: pd.Series, b_row: pd.Series) -> float:
    a_cat = normalize_arabic(str(a_row.get("Category Name",""))) + " " + normalize_arabic(str(a_row.get("SubCategory Name","")))
    b_cat = normalize_arabic(str(b_row.get("category_name",""))) + " " + normalize_arabic(str(b_row.get("section_name","")))
    a_tokens = set(a_cat.split()) if a_cat.strip() else set()
    b_tokens = set(b_cat.split()) if b_cat.strip() else set()
    if a_tokens and b_tokens:
        if a_tokens.isdisjoint(b_tokens):
            return -8.0
        inter = len(a_tokens & b_tokens)
        union = len(a_tokens | b_tokens) or 1
        j = inter / union
        if j >= 0.5:
            return 3.0
        if j >= 0.25:
            return 1.5
    return 0.0

# ----------------------------
# Scoring
# ----------------------------

def combined_score(a_text: str, b_text: str, tfidf_sim: float) -> float:
    tsr = fuzz.token_sort_ratio(a_text, b_text) / 100.0
    tset = fuzz.token_set_ratio(a_text, b_text) / 100.0
    pr = fuzz.partial_ratio(a_text, b_text) / 100.0
    fuzzy = 0.5 * tset + 0.3 * tsr + 0.2 * pr
    base = 0.5 * tfidf_sim + 0.5 * fuzzy
    return base * 100.0

def get_category_tolerances(a_core: str, b_core: str) -> Dict[str, float]:
    """
    Advanced ML-driven tolerance system based on product category analysis.
    Returns category-specific tolerance thresholds for different attributes.
    """
    # Base tolerances (conservative defaults)
    tolerances = {
        "weight_tolerance": 0.15,  # 15% default
        "volume_tolerance": 0.12,  # 12% default
        "pack_tolerance": 0.0,     # Exact match required
        "bonus_threshold": 0.05    # 5% for bonus
    }

    # Category-specific intelligence
    combined_text = f"{a_core} {b_core}".lower()

    # Beverages: allow small volume differences (225ml vs 200ml = 11% is acceptable)
    if any(token in combined_text for token in ["عصير", "مشروب", "كوكا", "بيبسي", "سبرايت"]):
        tolerances.update({
            "volume_tolerance": 0.15,  # 15% - allow 225ml vs 200ml differences
            "weight_tolerance": 0.20,  # 20% - cans vs bottles vary
            "pack_tolerance": 0.0      # Exact pack count critical
        })

    # Dairy & Fresh: very strict due to standardization
    elif any(token in combined_text for token in ["لبن", "جبن", "زبادي", "كريم"]):
        tolerances.update({
            "weight_tolerance": 0.08,  # 8% - highly standardized
            "volume_tolerance": 0.08,
            "bonus_threshold": 0.03
        })

    # Pasta/Noodles: STRICT weight matching (1kg vs 350g should never match)
    elif any(token in combined_text for token in ["مكرونة", "مكرونه", "باستا", "شعرية"]):
        tolerances.update({
            "weight_tolerance": 0.05,  # 5% - very strict for pasta
            "pack_tolerance": 0.0,
            "bonus_threshold": 0.02
        })

    # Jams/Preserves: strict weight matching
    elif any(token in combined_text for token in ["مربي", "مربى", "جام"]):
        tolerances.update({
            "weight_tolerance": 0.05,  # 5% - jars are standardized
            "volume_tolerance": 0.05,
            "bonus_threshold": 0.02
        })

    # Snacks & Confectionery: moderate tolerance for family packs
    elif any(token in combined_text for token in ["بسكويت", "شوكولاته", "حلوى", "شيبس"]):
        tolerances.update({
            "weight_tolerance": 0.12,  # 12% - some size variation
            "pack_tolerance": 0.0      # Piece count must match exactly
        })

    # Cleaning products: MUCH stricter tolerance (4.5kg vs 2.5kg should not match)
    elif any(token in combined_text for token in ["منظف", "مسحوق", "جل", "سائل", "صابون", "اريال", "كلوركس"]):
        tolerances.update({
            "weight_tolerance": 0.05,  # 5% - much stricter (44% diff should be rejected)
            "volume_tolerance": 0.05,  # 5% - much stricter
            "bonus_threshold": 0.02
        })

    # Baby products: extremely strict (32 vs 80 diapers should not match)
    elif any(token in combined_text for token in ["حفاض", "حفاضة", "حفاضات", "اطفال", "بيبي", "بى بم"]):
        tolerances.update({
            "weight_tolerance": 0.03,  # 3% - safety critical
            "pack_tolerance": 0.0,     # Exact count critical
            "bonus_threshold": 0.01
        })

    # Bouillon cubes: STRICT count matching (12 vs 8 should not match)
    elif any(token in combined_text for token in ["مرقة", "مرقه", "مكعب", "ماجي"]):
        tolerances.update({
            "weight_tolerance": 0.05,  # 5% - cubes are standardized
            "pack_tolerance": 0.0,     # Exact cube count critical
            "bonus_threshold": 0.02
        })

    # Bulk commodities: higher tolerance expected
    elif any(token in combined_text for token in ["ارز", "سكر", "دقيق", "زيت"]):
        tolerances.update({
            "weight_tolerance": 0.25,  # 25% - bulk variations common
            "volume_tolerance": 0.20,
            "bonus_threshold": 0.10
        })

    # Tissues/Napkins: strict layer and count matching
    elif any(token in combined_text for token in ["مناديل", "منديل", "tissue", "napkin"]):
        tolerances.update({
            "weight_tolerance": 0.08,  # 8% - standardized
            "pack_tolerance": 0.0,     # Exact count critical
            "bonus_threshold": 0.03
        })

    # Sanitary towels: extremely strict count matching
    elif any(token in combined_text for token in ["فوطه", "فوطة", "فوط", "مولبد", "اولويز", "always"]):
        tolerances.update({
            "weight_tolerance": 0.05,  # 5% - safety critical
            "pack_tolerance": 0.0,     # Exact count critical
            "bonus_threshold": 0.02
        })

    # Gum/Candy: strict piece count
    elif any(token in combined_text for token in ["لبان", "حلوى", "تشكلتس", "candy", "gum"]):
        tolerances.update({
            "weight_tolerance": 0.08,  # 8% - piece count matters
            "pack_tolerance": 0.0,     # Exact piece count critical
            "bonus_threshold": 0.03
        })

    # Tea/Coffee: moderate tolerance
    elif any(token in combined_text for token in ["شاي", "قهوة", "نسكافه"]):
        tolerances.update({
            "weight_tolerance": 0.15,  # 15% - standard
            "pack_tolerance": 0.0      # Bag/sachet count critical
        })

    return tolerances

def compare_attributes(a: Dict[str, Any], b: Dict[str, Any], reject_ratio: float = 0.25) -> Tuple[float, Optional[str]]:
    bonus = 0.0
    reason = None

    def rel_diff(x, y):
        if x is None or y is None or (isinstance(x, float) and math.isnan(x)) or (isinstance(y, float) and math.isnan(y)):
            return None
        denom = max(abs(x), abs(y), 1.0)
        return abs(x - y) / denom

    # Get intelligent category-based tolerances
    tolerances = get_category_tolerances(a.get("clean_core", ""), b.get("clean_core", ""))

    a_ml_u, b_ml_u = unify_liquid_units(a, b, a.get("clean_core",""), b.get("clean_core",""))

    # STRICTER attribute checking with category-aware tolerances
    attribute_checks = [
        ("net_weight_g", a.get("net_weight_g"), b.get("net_weight_g"),
         tolerances["weight_tolerance"], tolerances["bonus_threshold"]),
        ("volume_ml", a_ml_u, b_ml_u,
         tolerances["volume_tolerance"], tolerances["bonus_threshold"]),
        ("teabags_count", a.get("teabags_count"), b.get("teabags_count"),
         0.0, 0.0),  # Tea bags must match exactly
        ("diapers_count", a.get("diapers_count"), b.get("diapers_count"),
         0.0, 0.0),  # Diaper count must match exactly
        ("cube_count", a.get("cube_count"), b.get("cube_count"),
         0.0, 0.0),  # Bouillon cube count must match exactly
        ("towel_count", a.get("towel_count"), b.get("towel_count"),
         0.0, 0.0),  # Sanitary towel count must match exactly
        ("tissue_layers", a.get("tissue_layers"), b.get("tissue_layers"),
         0.0, 0.0),  # Tissue layers must match exactly (2 vs 3 layer)
        ("tissue_count", a.get("tissue_count"), b.get("tissue_count"),
         0.05, 0.02),  # Tissue count with small tolerance (500 vs 650 might be acceptable)
        ("roll_count", a.get("roll_count"), b.get("roll_count"),
         0.0, 0.0),  # Roll count must match exactly (12 vs 2 rolls)
    ]

    for attr_name, av, bv, tolerance, bonus_thresh in attribute_checks:
        # Special check: if one has volume and other has weight, it's a mismatch
        if attr_name == "volume_ml" and av is not None and (bv is None or math.isnan(bv)):
            # A has volume, B doesn't - check if B has weight instead
            b_weight = b.get("net_weight_g")
            if b_weight is not None and not math.isnan(b_weight):
                return -50.0, "volume_vs_weight_mismatch"
        elif attr_name == "net_weight_g" and av is not None and (bv is None or math.isnan(bv)):
            # A has weight, B doesn't - check if B has volume instead
            b_volume = b.get("volume_ml")
            if b_volume is not None and not math.isnan(b_volume):
                return -50.0, "weight_vs_volume_mismatch"

        d = rel_diff(av, bv)
        if d is None:
            continue

        # STRICTER rejection criteria for critical attributes
        if attr_name in ["diapers_count", "cube_count", "teabags_count", "towel_count", "tissue_layers", "roll_count"]:
            # For critical counts, must be exact or very close
            if d > 0.05:  # 5% tolerance for counts
                return -40.0, f"{attr_name}_mismatch_{int(d*100)}%_too_large"
        elif attr_name == "tissue_count":
            # Tissue count can have slightly more tolerance (500 vs 650 might be acceptable)
            if d > 0.25:  # 25% tolerance for tissue count
                return -35.0, f"{attr_name}_mismatch_{int(d*100)}%_too_large"
        elif d > tolerance:
            return -35.0, f"{attr_name}_mismatch_{int(d*100)}%_exceeds_{int(tolerance*100)}%"

        # Additional check for large weight/volume differences that might indicate different products
        if attr_name in ["net_weight_g", "volume_ml"]:
            # Very large differences indicate different products (450ml vs 110g, 500ml vs 1L)
            if d > 0.4:  # 40% difference - clearly different products
                return -50.0, f"very_large_{attr_name}_difference_{int(d*100)}%"
            elif d > 0.2:  # 20% difference - likely different products
                return -45.0, f"large_{attr_name}_difference_{int(d*100)}%"

        # Sophisticated scoring based on ML insights
        if d <= bonus_thresh:
            bonus += 15.0  # Strong bonus for very close matches
        elif d <= tolerance * 0.3:
            bonus += 10.0   # Good bonus for reasonably close
        elif d <= tolerance * 0.6:
            bonus += 5.0   # Small bonus for acceptable range
        else:
            bonus -= 8.0   # Penalty for borderline matches

    # Pack count is noisy and unreliable - do NOT use for rejection
    # Only use as a small positive tie-breaker when both sides agree exactly
    pc_a = a.get("pack_count")
    pc_b = b.get("pack_count")
    if not (isinstance(pc_a, float) and math.isnan(pc_a)) and not (isinstance(pc_b, float) and math.isnan(pc_b)):
        try:
            ia = int(round(float(pc_a)))
            ib = int(round(float(pc_b)))
            if ia == ib:
                bonus += 3.0  # Small bonus for exact match only
            # else: ignore differences (no penalty or rejection)
        except Exception:
            pass

    sem_reason = hard_semantic_filter(a, b)
    if sem_reason:
        return -30.0, sem_reason

    return bonus, reason

# ----------------------------
# Brand normalization + aliasing
# ----------------------------

def normalize_brand(text: str) -> str:
    t = normalize_arabic(text)

    # Remove company suffixes
    t = re.sub(r"\b(شركه|شركة|كو|المحدوده|محدوده|المصريه|المصرية|ايجيبت|جروب|ماركه|براند|صناعات)\b", " ", t)

    # Remove family/lineage prefixes and suffixes
    t = re.sub(r"\bبن\s+", "", t)  # Remove "بن" (son of) prefix
    t = re.sub(r"\bابن\s+", "", t)  # Remove "ابن" (son of) prefix
    t = re.sub(r"\bبنت\s+", "", t)  # Remove "بنت" (daughter of) prefix
    t = re.sub(r"\s+اخوان\b", "", t)  # Remove "اخوان" (brothers) suffix

    # Remove category suffixes that are added to brand names (جهينه عصير -> جهينه)
    category_suffixes = [
        "عصير", "عصائر", "لبن", "البان", "مشروبات", "مشروب", "منتجات", "منتج",
        "اغذيه", "اغذية", "طعام", "غذاء", "صناعات", "انتاج", "تصنيع"
    ]

    for suffix in category_suffixes:
        # Remove suffix if it appears at the end
        pattern = rf"\b{suffix}\b\s*$"
        t = re.sub(pattern, "", t)
        # Also remove if it appears after the main brand name
        pattern = rf"\b{suffix}\b"
        if len(t.split()) > 1:  # Only if there are multiple words
            t = re.sub(pattern, "", t)

    t = re.sub(r"\s+", " ", t).strip()
    return t

# Common brand overrides (canonicalization of spelling variants)
BRAND_OVERRIDES = {
    "بيبسى": "بيبسي", "بيتي": "بيتي", "جهينه": "جهينة", "دومتى": "دومتي",
    "اوكسى": "اوكسي", "اوكسى": "اوكسي", "اوكسي": "اوكسي",
    "برل": "بريل", "بر يل": "بريل", "بريل": "بريل",
    "كرونا": "كورونا", "كورانا": "كورونا", "كورونا": "كورونا",
    "جلاكسي": "جلاكسي", "جالاكسي": "جلاكسي",
    "تويكس": "تويكس", "مارس": "مارس", "سنيكرز": "سنيكرز",
    "ابو الولد": "ابو الولد", "ابو الولاد": "ابو الولد",
    "بيبي جوي": "بيبي جوي", "بيبيجوي": "بيبي جوي", "بى بم": "بي بم", "بي بم": "بي بم",
    "دولفين": "دولفين", "حلوانى": "حلواني", "حلواني": "حلواني",
    "ماكسى": "ماكسي", "ماكسي": "ماكسي",
    "جهينه": "جهينة", "حلوانى اخوان": "حلواني اخوان",
}

def apply_brand_overrides(b: str) -> str:
    return BRAND_OVERRIDES.get(b, b)


def build_brand_alias_map(brands_left: List[str], brands_right: List[str], threshold: int = 92) -> Dict[str, str]:
    left_norm = [normalize_brand(b) for b in brands_left if isinstance(b, str) and str(b).strip()]
    right_norm = [normalize_brand(b) for b in brands_right if isinstance(b, str) and str(b).strip()]
    right_set = sorted(set(right_norm))
    alias: Dict[str, str] = {}
    for b in left_norm:
        if b in right_set:
            alias[b] = b
    for b in left_norm:
        if b in alias or not b:
            continue
        best = None
        best_score = -1
        for r in right_set:
            s = fuzz.token_sort_ratio(b, r)
            if s > best_score:
                best_score = s
                best = r
        if best_score >= threshold:
            alias[b] = best
        else:
            alias[b] = b
    for r in right_set:
        alias[r] = r
    return alias

def infer_brand_from_name(name_clean: str, brand_vocab: List[str]) -> Optional[str]:
    best = None
    for b in brand_vocab:
        if not b:
            continue
        if re.search(rf"(^|\s){re.escape(b)}(\s|$)", name_clean):
            if best is None or len(b) > len(best):
                best = b
    return best

# ----------------------------
# Data IO helpers
# ----------------------------

def ensure_string(s: pd.Series) -> pd.Series:
    return s.fillna("").astype(str)

def assemble_maxab_unit(row: pd.Series) -> str:
    child_count = row.get("defaultUnit_child_unit_count")
    child_name = row.get("defaultUnit_child_unit_name")
    desc = row.get("defaultUnit_description")
    def as_int_like(x):
        try:
            f = float(str(x).strip())
            return str(int(round(f)))
        except Exception:
            return None
    if pd.notna(child_count) and pd.notna(child_name) and str(child_name).strip():
        num = as_int_like(child_count)
        if num:
            return f"{num} {child_name}".strip()
    if isinstance(desc, str) and desc.strip():
        return desc.strip()
    name = row.get("defaultUnit_name")
    if isinstance(name, str) and name.strip():
        return name.strip()
    return ""

def assemble_talabeyah_unit(row: pd.Series) -> str:
    s = row.get("Selling Unit Name")
    if isinstance(s, str) and s.strip():
        return s.strip()
    features = build_features(str(row.get("__name__") or ""))
    pc = features.get("pack_count")
    unit = features.get("item_size_unit")
    if not (isinstance(pc, float) and math.isnan(pc)) and unit:
        try:
            return f"{int(pc)} {unit}"
        except Exception:
            return f"{pc} {unit}"
    return ""

# ----------------------------
# Embeddings (optional)
# ----------------------------

def get_embed_model():
    global EMBED_MODEL
    if EMBED_MODEL is None:
        try:
            from sentence_transformers import SentenceTransformer
        except ImportError as e:
            raise RuntimeError("sentence-transformers not installed. Run: pip install sentence-transformers") from e
        EMBED_MODEL = SentenceTransformer("sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
    return EMBED_MODEL

def embed_texts(texts: List[str]) -> np.ndarray:
    model = get_embed_model()
    return np.array(model.encode(texts, normalize_embeddings=True, show_progress_bar=False))

# ----------------------------
# Main matching
# ----------------------------

def match_products_brand_blocked(
    talab_df: pd.DataFrame,
    maxab_df: pd.DataFrame,
    threshold: float = 85.0,
    brand_threshold: int = 92,
    use_embeddings: bool = False,
    topk: int = 10
):
    # Columns
    talab_name_col = "Name" if "Name" in talab_df.columns else talab_df.columns[0]
    talab_brand_col = "Brand Name" if "Brand Name" in talab_df.columns else None
    talab_unit_col = "Selling Unit Name" if "Selling Unit Name" in talab_df.columns else None
    talab_pack_col = "Num Of Units Per Case" if "Num Of Units Per Case" in talab_df.columns else None

    maxab_name_col = "product_name" if "product_name" in maxab_df.columns else maxab_df.columns[0]
    maxab_brand_col = "brand_name" if "brand_name" in maxab_df.columns else None
    maxab_pack_col = "defaultUnit_child_unit_count" if "defaultUnit_child_unit_count" in maxab_df.columns else None

    L = talab_df.copy()
    R = maxab_df.copy()

    L["_orig_index"] = L.index

    # Name + unit
    L["__name__"] = ensure_string(L[talab_name_col])
    R["__name__"] = ensure_string(R[maxab_name_col])
    L["__unit__"] = ensure_string(L[talab_unit_col]) if talab_unit_col and talab_unit_col in L.columns else ""
    R["__unit__"] = R.apply(assemble_maxab_unit, axis=1)

    # Features from names
    L_feats = L["__name__"].apply(build_features).apply(pd.Series)
    R_feats = R["__name__"].apply(build_features).apply(pd.Series)
    L = pd.concat([L, L_feats], axis=1)
    R = pd.concat([R, R_feats], axis=1)

    # Inject pack_count from columns if available
    if talab_pack_col and talab_pack_col in L.columns:
        L["pack_count"] = np.where(L["pack_count"].isna(), pd.to_numeric(L[talab_pack_col], errors="coerce"), L["pack_count"])
    if maxab_pack_col and maxab_pack_col in R.columns:
        R["pack_count"] = np.where(R["pack_count"].isna(), pd.to_numeric(R[maxab_pack_col], errors="coerce"), R["pack_count"])

    # Brands
    L["brand_raw"] = ensure_string(L[talab_brand_col]) if talab_brand_col else ""
    R["brand_raw"] = ensure_string(R[maxab_brand_col]) if maxab_brand_col else ""
    L["brand_norm"] = L["brand_raw"].apply(normalize_brand)
    R["brand_norm"] = R["brand_raw"].apply(normalize_brand)

    alias_map = build_brand_alias_map(
        L["brand_raw"].dropna().unique().tolist(),
        R["brand_raw"].dropna().unique().tolist(),
        threshold=brand_threshold
    )
    brand_vocab = sorted(set(alias_map.keys()) | set(alias_map.values()), key=lambda s: len(s), reverse=True)
    L["brand_from_name"] = L["clean_name"].apply(lambda s: infer_brand_from_name(s, brand_vocab) or "")
    R["brand_from_name"] = R["clean_name"].apply(lambda s: infer_brand_from_name(s, brand_vocab) or "")

    def canon(b_norm: str, b_from: str) -> str:
        base = normalize_brand(b_norm if b_norm else b_from)
        base = apply_brand_overrides(base)
        return alias_map.get(base, base) if base else ""

    L["brand_canonical"] = [canon(n, f) for n, f in zip(L["brand_norm"], L["brand_from_name"])]
    R["brand_canonical"] = [canon(n, f) for n, f in zip(R["brand_norm"], R["brand_from_name"])]

    # Matching
    matched_rows = []
    best_suggestion: Dict[int, Dict[str, Any]] = {}
    matched_L, matched_R = set(), set()

    brands = [b for b in sorted(set(L["brand_canonical"])) if b]
    for brand in tqdm(brands, desc="Brands", unit="brand"):
        Lb = L[L["brand_canonical"] == brand]
        Rb = R[R["brand_canonical"] == brand]
        if Lb.empty or Rb.empty:
            continue
        if all(not str(x).strip() for x in Rb["clean_core"]) or all(not str(x).strip() for x in Lb["clean_core"]):
            continue

        Rb_idx_list = Rb.index.to_list()
        n_docs = Rb.shape[0]
        effective_max_df = 1.0 if n_docs < 2 else 0.95

        candidates_for_left: Optional[List[List[int]]] = None

        # Optional embedding-based candidate narrowing
        if use_embeddings:
            R_texts = Rb["clean_core"].tolist()
            L_texts = Lb["clean_core"].tolist()
            if R_texts and L_texts:
                R_emb = embed_texts(R_texts)
                L_emb = embed_texts(L_texts)
                sims = L_emb @ R_emb.T
                k = min(max(3, topk), Rb.shape[0])
                topk_idx = np.argpartition(-sims, kth=k-1, axis=1)[:, :k]
                candidates_for_left = [
                    [Rb.index[j] for j in topk_idx[i].tolist()]
                    for i in range(Lb.shape[0])
                ]

        use_fallback = False
        try:
            vectorizer = TfidfVectorizer(analyzer="word", ngram_range=(1, 3), min_df=1, max_df=effective_max_df, lowercase=False)
            R_mat = vectorizer.fit_transform(Rb["clean_core"])
            L_mat = vectorizer.transform(Lb["clean_core"])
            nn = NearestNeighbors(n_neighbors=min(8, max(1, n_docs)), metric="cosine").fit(R_mat)
            distances, indices = nn.kneighbors(L_mat, return_distance=True)
        except ValueError:
            use_fallback = True

        if not use_fallback:
            Lb_indices = Lb.index.to_list()
            for i_left, idx_left in enumerate(tqdm(Lb_indices, desc="Items", unit="item", leave=False)):
                left_row = L.loc[idx_left]
                best = {"score": -1.0, "j": None, "reason": None}

                # Build candidate list: from embeddings if present else from NN neighbors
                if candidates_for_left is not None:
                    candidate_js = candidates_for_left[i_left]
                else:
                    candidate_js = [Rb_idx_list[int(jpos)] for jpos in indices[i_left]]

                for j in candidate_js:
                    right_row = R.loc[j]
                    # If not using embeddings, we have a distance for the neighbor; approximate tfidf_sim
                    if candidates_for_left is None:
                        # find position in neighbor list to get distance
                        # if missing, approximate via vectorizer similarity
                        try:
                            pos = [Rb_idx_list[int(jpos)] for jpos in indices[i_left]].index(j)
                            tfidf_sim = 1.0 - distances[i_left][pos]
                        except Exception:
                            # fallback cosine sim
                            tfidf_sim = 0.0
                    else:
                        # when using embeddings, we still compute a TF-IDF similarity via vectorizer on the fly
                        # quick approximate using fuzz only
                        tfidf_sim = 0.0

                    base = combined_score(left_row["clean_core"], right_row["clean_core"], tfidf_sim)
                    sem = semantic_bonus(left_row["clean_core"], right_row["clean_core"])
                    attr_bonus, reason = compare_attributes(left_row.to_dict(), right_row.to_dict())
                    cat_pen = category_penalty(left_row, right_row)
                    total = base + sem + attr_bonus + cat_pen

                    cand_reason = reason if reason else None
                    if total > best["score"]:
                        best = {"score": total, "j": j, "reason": cand_reason}

                if best["j"] is not None:
                    r = R.loc[best["j"]]
                    best_suggestion[idx_left] = {
                        "Brand": brand,
                        "Suggested Maxab": r["__name__"],
                        "Suggested Unit": r["__unit__"],
                        "Suggested Score": round(best["score"], 1),
                        "Reject Reason": best["reason"] or ("below_threshold" if best["score"] < threshold else None),
                    }

                if best["j"] is not None and best["score"] >= threshold and not best["reason"]:
                    r = R.loc[best["j"]]
                    matched_rows.append({
                        "Brand": brand,
                        "Talabeyah Product": left_row["__name__"],
                        "Talabeyah Unit": left_row["__unit__"],
                        "Maxab Product": r["__name__"],
                        "Maxab Unit": r["__unit__"],
                        "score_value": best["score"],
                    })
                    matched_L.add(idx_left)
                    matched_R.add(best["j"])
        else:
            for idx_left in tqdm(Lb.index.to_list(), desc="Items", unit="item", leave=False):
                left_row = L.loc[idx_left]
                best = {"score": -1.0, "j": None, "reason": None}
                for j in Rb_idx_list:
                    right_row = R.loc[j]
                    tsr = fuzz.token_sort_ratio(left_row["clean_core"], right_row["clean_core"])
                    sem = semantic_bonus(left_row["clean_core"], right_row["clean_core"])
                    attr_bonus, reason = compare_attributes(left_row.to_dict(), right_row.to_dict())
                    cat_pen = category_penalty(left_row, right_row)
                    total = tsr + sem + attr_bonus + cat_pen
                    if total > best["score"]:
                        best = {"score": total, "j": j, "reason": reason}
                if best["j"] is not None:
                    r = R.loc[best["j"]]
                    best_suggestion[idx_left] = {
                        "Brand": brand,
                        "Suggested Maxab": r["__name__"],
                        "Suggested Unit": r["__unit__"],
                        "Suggested Score": round(best["score"], 1),
                        "Reject Reason": best["reason"] or ("below_threshold" if best["score"] < threshold else None),
                    }
                if best["j"] is not None and best["score"] >= threshold and not best["reason"]:
                    r = R.loc[best["j"]]
                    matched_rows.append({
                        "Brand": brand,
                        "Talabeyah Product": left_row["__name__"],
                        "Talabeyah Unit": left_row["__unit__"],
                        "Maxab Product": r["__name__"],
                        "Maxab Unit": r["__unit__"],
                        "score_value": best["score"],
                    })
                    matched_L.add(idx_left)
                    matched_R.add(best["j"])

    # Global fallback for still-unmatched left items
    left_unmatched_idx = [i for i in L.index if i not in matched_L]
    if left_unmatched_idx:
        valid_right = R["clean_core"].apply(lambda s: bool(str(s).strip()))
        R_all = R[valid_right]
        if not R_all.empty:
            # Embedding-based global narrowing if enabled
            candidates_global: Optional[Dict[int, List[int]]] = None
            if use_embeddings:
                R_texts = R_all["clean_core"].tolist()
                L_texts = L.loc[left_unmatched_idx, "clean_core"].tolist()
                R_emb = embed_texts(R_texts)
                L_emb = embed_texts(L_texts)
                sims = L_emb @ R_emb.T
                k = min(max(5, topk), R_all.shape[0])
                topk_idx = np.argpartition(-sims, kth=k-1, axis=1)[:, :k]
                R_all_idx_list = R_all.index.to_list()
                candidates_global = {
                    left_unmatched_idx[i]: [R_all_idx_list[j] for j in topk_idx[i].tolist()]
                    for i in range(len(left_unmatched_idx))
                }

            vectorizer_g = TfidfVectorizer(analyzer="word", ngram_range=(1, 3), min_df=1, max_df=0.98, lowercase=False)
            R_mat_all = vectorizer_g.fit_transform(R_all["clean_core"])
            nn_g = NearestNeighbors(n_neighbors=min(25, max(1, R_all.shape[0])), metric="cosine").fit(R_mat_all)
            R_all_idx_list = R_all.index.to_list()

            for idx_left in tqdm(left_unmatched_idx, desc="Fallback", unit="item"):
                if idx_left in matched_L:
                    continue
                left_row = L.loc[idx_left]
                if not str(left_row["clean_core"]).strip():
                    continue

                # Build candidate list
                if candidates_global is not None and idx_left in candidates_global:
                    candidate_js = candidates_global[idx_left]
                    # No tfidf sim available; use 0.0 and rely on fuzz + attrs + semantic
                    best = {"score": -1.0, "j": None, "reason": None}
                    for j in candidate_js:
                        if j in matched_R:
                            continue
                        right_row = R.loc[j]
                        # Enforce brand consistency in global fallback
                        lb = left_row.get("brand_canonical", "")
                        rb = right_row.get("brand_canonical", "")
                        if (not lb) or (not rb) or (lb != rb):
                            continue
                        base = combined_score(left_row["clean_core"], right_row["clean_core"], 0.0)
                        sem = semantic_bonus(left_row["clean_core"], right_row["clean_core"])
                        attr_bonus, reason = compare_attributes(left_row.to_dict(), right_row.to_dict())
                        cat_pen = category_penalty(left_row, right_row)
                        total = base + sem + attr_bonus + cat_pen
                        if total > best["score"]:
                            best = {"score": total, "j": j, "reason": reason}
                else:
                    # Pure TF-IDF global NN
                    L_vec = vectorizer_g.transform([left_row["clean_core"]])
                    dist_g, ind_g = nn_g.kneighbors(L_vec, return_distance=True)
                    best = {"score": -1.0, "j": None, "reason": None}
                    for rank, jpos in enumerate(ind_g[0]):
                        j = R_all_idx_list[int(jpos)]
                        if j in matched_R:
                            continue
                        right_row = R.loc[j]
                        # Enforce brand consistency in global fallback
                        lb = left_row.get("brand_canonical", "")
                        rb = right_row.get("brand_canonical", "")
                        if (not lb) or (not rb) or (lb != rb):
                            continue
                        tfidf_sim = 1.0 - dist_g[0][rank]
                        base = combined_score(left_row["clean_core"], right_row["clean_core"], tfidf_sim)
                        sem = semantic_bonus(left_row["clean_core"], right_row["clean_core"])
                        attr_bonus, reason = compare_attributes(left_row.to_dict(), right_row.to_dict())
                        cat_pen = category_penalty(left_row, right_row)
                        total = base + sem + attr_bonus + cat_pen
                        if total > best["score"]:
                            best = {"score": total, "j": j, "reason": reason}

                if best["j"] is not None:
                    r = R.loc[best["j"]]
                    best_suggestion[idx_left] = {
                        "Brand": r.get("brand_canonical",""),
                        "Suggested Maxab": r["__name__"],
                        "Suggested Unit": r["__unit__"],
                        "Suggested Score": round(best["score"], 1),
                        "Reject Reason": best["reason"] or ("below_threshold" if best["score"] < threshold else None),
                    }

                if best["j"] is not None and best["score"] >= threshold and not best["reason"]:
                    r = R.loc[best["j"]]
                    matched_rows.append({
                        "Brand": r.get("brand_canonical",""),
                        "Talabeyah Product": left_row["__name__"],
                        "Talabeyah Unit": left_row["__unit__"],
                        "Maxab Product": r["__name__"],
                        "Maxab Unit": r["__unit__"],
                        "score_value": best["score"],
                    })
                    matched_L.add(idx_left)
                    matched_R.add(best["j"])

    # POST-PROCESSING: Remove one-to-many matches, keep only the best match for each right item
    matched_df = pd.DataFrame(matched_rows)
    if not matched_df.empty:
        # Sort by score first
        matched_df = matched_df.sort_values(by="score_value", ascending=False).reset_index(drop=True)

        print(f"Before deduplication: {len(matched_df)} matches")

        # Group by right side product and keep only the best match
        if "Maxab Product" in matched_df.columns:
            matched_df = matched_df.groupby("Maxab Product").first().reset_index()

        print(f"After right-side deduplication: {len(matched_df)} matches")

        # Also remove duplicate matches on the left side (keep highest scoring)
        if "Talabeyah Product" in matched_df.columns:
            matched_df = matched_df.sort_values(by="score_value", ascending=False)
            matched_df = matched_df.groupby("Talabeyah Product").first().reset_index()

        print(f"After left-side deduplication: {len(matched_df)} matches")

        matched_df = matched_df.sort_values(by="score_value", ascending=False).reset_index(drop=True)
        matched_df["Match Score"] = matched_df["score_value"].round(1).astype(str) + "%"
        matched_df = matched_df.drop(columns=["score_value"])

    # Unmatched (only-in)
    L_only = L.loc[~L.index.isin(matched_L)].copy()
    R_only = R.loc[~R.index.isin(matched_R)].copy()

    left_cols = ["_orig_index", "brand_canonical", "__name__", "__unit__", "clean_core", "net_weight_g", "volume_ml", "pack_count", "teabags_count", "diapers_count", "diaper_size"]
    left_cols = [c for c in left_cols if c in L_only.columns]
    left_only_df = L_only[left_cols].rename(columns={
        "brand_canonical": "Brand",
        "__name__": "Talabeyah Product",
        "__unit__": "Talabeyah Unit",
    }).reset_index(drop=True)

    # Attach suggestions using original index
    suggest_cols = ["Suggested Maxab", "Suggested Unit", "Suggested Score", "Reject Reason"]
    for col in suggest_cols:
        left_only_df[col] = ""
    if "_orig_index" in left_only_df.columns:
        for i, orig_idx in enumerate(L_only["_orig_index"].tolist()):
            s = best_suggestion.get(orig_idx, {})
            for col in suggest_cols:
                left_only_df.at[i, col] = s.get(col, "")

        left_only_df = left_only_df.drop(columns=["_orig_index"])

    right_cols = ["brand_canonical", "__name__", "__unit__", "clean_core", "net_weight_g", "volume_ml", "pack_count", "teabags_count", "diapers_count", "diaper_size"]
    right_cols = [c for c in right_cols if c in R_only.columns]
    right_only_df = R_only[right_cols].rename(columns={
        "brand_canonical": "Brand",
        "__name__": "Maxab Product",
        "__unit__": "Maxab Unit",
    }).reset_index(drop=True)

    return matched_df, left_only_df, right_only_df

# ----------------------------
# CLI
# ----------------------------

def main():
    parser = argparse.ArgumentParser(description="Match Talabeyah and MaxAB products from a single Excel workbook.")
    parser.add_argument("--workbook", default=r"C:\Users\<USER>\OneDrive\Documents\halan internship\talabeyah_and_maxab.xlsx", help="Path to the Excel workbook containing both sheets")
    parser.add_argument("--maxab-sheet", default="Maxab products", help="Sheet name for Maxab products")
    parser.add_argument("--talab-sheet", default="Talabeyah Products", help="Sheet name for Talabeyah products")
    parser.add_argument("--output", default="talabeyah_maxab_matchesgpt5v14newid0.xlsx", help="Output Excel filename")
    parser.add_argument("--threshold", type=float, default=0.10, help="Match acceptance threshold (0-100)")
    parser.add_argument("--brand-threshold", type=int, default=90, help="Fuzzy brand alias threshold (0-100)")
    parser.add_argument("--use-embeddings", action="store_true", help="Use Arabic embeddings for candidate generation")
    parser.add_argument("--topk", type=int, default=12, help="Top-K candidates per item when using embeddings")
    args = parser.parse_args()

    talab_df = pd.read_excel(args.workbook, sheet_name=args.talab_sheet)
    maxab_df = pd.read_excel(args.workbook, sheet_name=args.maxab_sheet)

    matched_df, left_only_df, right_only_df = match_products_brand_blocked(
        talab_df, maxab_df,
        threshold=args.threshold,
        brand_threshold=args.brand_threshold,
        use_embeddings=args.use_embeddings,
        topk=args.topk
    )

    # Enrich output DataFrames with SKU/ID columns from original sheets (output-only change)
    # Detect column names robustly
    t_name_col = "Name" if "Name" in talab_df.columns else talab_df.columns[0]
    t_sku_col = "SKU Code" if "SKU Code" in talab_df.columns else None
    m_name_col = "product_name" if "product_name" in maxab_df.columns else maxab_df.columns[0]
    m_id_col = "product_id" if "product_id" in maxab_df.columns else None

    # Build lookup maps
    talab_map = None
    if t_sku_col is not None:
        tmp = talab_df[[t_name_col, t_sku_col]].dropna(subset=[t_name_col]).copy()
        tmp[t_name_col] = tmp[t_name_col].astype(str)
        talab_map = dict(zip(tmp[t_name_col], tmp[t_sku_col]))

    maxab_map = None
    if m_id_col is not None:
        tmp = maxab_df[[m_name_col, m_id_col]].dropna(subset=[m_name_col]).copy()
        tmp[m_name_col] = tmp[m_name_col].astype(str)
        maxab_map = dict(zip(tmp[m_name_col], tmp[m_id_col]))

    # Add codes to matched sheet
    if matched_df is not None and not matched_df.empty:
        if t_sku_col is not None and talab_map is not None and "Talabeyah Product" in matched_df.columns:
            matched_df["Talabeyah SKU Code"] = matched_df["Talabeyah Product"].astype(str).map(talab_map)
        if m_id_col is not None and maxab_map is not None and "Maxab Product" in matched_df.columns:
            matched_df["Maxab product_id"] = matched_df["Maxab Product"].astype(str).map(maxab_map)

    # Add codes to only-in Talabeyah
    if "Talabeyah Product" in left_only_df.columns and t_sku_col is not None and talab_map is not None:
        left_only_df["Talabeyah SKU Code"] = left_only_df["Talabeyah Product"].astype(str).map(talab_map)

    # Add codes to only-in Maxab
    if "Maxab Product" in right_only_df.columns and m_id_col is not None and maxab_map is not None:
        right_only_df["Maxab product_id"] = right_only_df["Maxab Product"].astype(str).map(maxab_map)


    with pd.ExcelWriter(args.output, engine="openpyxl") as writer:
        if matched_df is not None and not matched_df.empty:
            matched_df.to_excel(writer, index=False, sheet_name="matched")
        left_only_df.to_excel(writer, index=False, sheet_name="only_in_talabeyah")
        right_only_df.to_excel(writer, index=False, sheet_name="only_in_maxab")

    out_path = os.path.abspath(args.output)
    print(f"Done. Saved to {out_path}")
    print(f"- matched: {0 if matched_df is None else matched_df.shape[0]}")
    print(f"- only_in_talabeyah: {left_only_df.shape[0]}")
    print(f"- only_in_maxab: {right_only_df.shape[0]}")

if __name__ == "__main__":
    main()