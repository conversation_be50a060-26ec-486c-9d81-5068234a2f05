<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Matcher - Enhanced</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-weight: bold;
        }
        .step.active .step-number {
            background-color: #007bff;
            color: white;
        }
        .step.completed .step-number {
            background-color: #28a745;
            color: white;
        }
        .column-mapping {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .preview-table {
            max-height: 200px;
            overflow-y: auto;
        }
        .language-badge {
            font-size: 0.8rem;
        }
        .hidden {
            display: none;
        }
        .loading {
            text-align: center;
            padding: 2rem;
        }
        .results-summary {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-search me-2"></i>
                    Product Matcher
                </h1>
                <p class="text-center text-muted">Upload an Excel file with two sheets to match products between companies</p>
            </div>
        </div>

        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <span>Upload File</span>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <span>Configure Columns</span>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <span>Match Products</span>
            </div>
            <div class="step" id="step4">
                <div class="step-number">4</div>
                <span>Download Results</span>
            </div>
        </div>

        <!-- Step 1: File Upload -->
        <div id="upload-section" class="card">
            <div class="card-header">
                <h5><i class="fas fa-upload me-2"></i>Step 1: Upload Excel File</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="fileInput" class="form-label">Select Excel file (.xlsx or .xls)</label>
                    <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls">
                    <div class="form-text">File should contain exactly 2 sheets, each representing a different company's products.</div>
                </div>
                <button type="button" class="btn btn-primary" id="uploadBtn" disabled>
                    <i class="fas fa-upload me-2"></i>Upload and Analyze
                </button>
            </div>
        </div>

        <!-- Step 2: Column Configuration -->
        <div id="config-section" class="card hidden">
            <div class="card-header">
                <h5><i class="fas fa-cogs me-2"></i>Step 2: Configure Column Mapping</h5>
            </div>
            <div class="card-body">
                <div id="sheets-config"></div>
                <div class="mt-3">
                    <label for="threshold" class="form-label">Matching Threshold (0-100)</label>
                    <input type="range" class="form-range" id="threshold" min="0" max="100" value="60">
                    <div class="d-flex justify-content-between">
                        <small>0 (Loose)</small>
                        <small id="threshold-value">60</small>
                        <small>100 (Strict)</small>
                    </div>
                </div>
                <button type="button" class="btn btn-success mt-3" id="configureBtn">
                    <i class="fas fa-check me-2"></i>Save Configuration
                </button>
            </div>
        </div>

        <!-- Step 3: Matching -->
        <div id="matching-section" class="card hidden">
            <div class="card-header">
                <h5><i class="fas fa-sync-alt me-2"></i>Step 3: Start Matching</h5>
            </div>
            <div class="card-body">
                <p>Configuration saved successfully. Click the button below to start the matching process.</p>
                <button type="button" class="btn btn-primary" id="matchBtn">
                    <i class="fas fa-play me-2"></i>Start Matching
                </button>
                <div id="matching-progress" class="loading hidden">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Matching products... This may take a few minutes.</p>
                </div>
            </div>
        </div>

        <!-- Step 4: Results -->
        <div id="results-section" class="card hidden">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>Step 4: Results</h5>
            </div>
            <div class="card-body">
                <div id="results-summary" class="results-summary"></div>
                <div id="results-preview"></div>
                <div class="mt-3">
                    <button type="button" class="btn btn-success" id="downloadBtn">
                        <i class="fas fa-download me-2"></i>Download Results
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" id="newMatchBtn">
                        <i class="fas fa-redo me-2"></i>Start New Match
                    </button>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alerts"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let fileId = null;
        let resultId = null;
        let sheetsInfo = {};

        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = !e.target.files.length;
        });

        // Threshold slider
        document.getElementById('threshold').addEventListener('input', function(e) {
            document.getElementById('threshold-value').textContent = e.target.value;
        });

        // Upload button click
        document.getElementById('uploadBtn').addEventListener('click', uploadFile);

        // Configure button click
        document.getElementById('configureBtn').addEventListener('click', saveConfiguration);

        // Match button click
        document.getElementById('matchBtn').addEventListener('click', startMatching);

        // Download button click
        document.getElementById('downloadBtn').addEventListener('click', downloadResults);

        // New match button click
        document.getElementById('newMatchBtn').addEventListener('click', startNewMatch);

        function updateStepIndicator(step) {
            // Reset all steps
            for (let i = 1; i <= 4; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');
                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function showAlert(message, type = 'danger') {
            const alertsDiv = document.getElementById('alerts');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertsDiv.innerHTML = alertHtml;
        }

        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showAlert('Please select a file first.');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // Show loading state
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    fileId = data.file_id;
                    sheetsInfo = data.sheets;
                    showSheetsConfiguration(data.sheets);
                    currentStep = 2;
                    updateStepIndicator(currentStep);
                    document.getElementById('upload-section').classList.add('hidden');
                    document.getElementById('config-section').classList.remove('hidden');
                    showAlert('File uploaded successfully!', 'success');
                } else {
                    showAlert(data.error || 'Upload failed.');
                }
            })
            .catch(error => {
                showAlert('Upload failed: ' + error.message);
            })
            .finally(() => {
                uploadBtn.disabled = false;
                uploadBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Upload and Analyze';
            });
        }

        function showSheetsConfiguration(sheets) {
            const configDiv = document.getElementById('sheets-config');
            const sheetNames = Object.keys(sheets);

            console.log('Sheets received:', sheets); // Debug log
            console.log('Sheet names:', sheetNames); // Debug log

            if (sheetNames.length !== 2) {
                showAlert('Excel file must contain exactly 2 sheets. Found: ' + sheetNames.length);
                return;
            }

            let configHtml = '';
            sheetNames.forEach((sheetName, index) => {
                const sheet = sheets[sheetName];
                const companyNum = index + 1;

                console.log(`Processing sheet ${sheetName}:`, sheet); // Debug log
                
                configHtml += `
                    <div class="column-mapping">
                        <h6>
                            Company ${companyNum}: ${sheetName} 
                            <span class="badge bg-${sheet.language === 'arabic' ? 'warning' : 'info'} language-badge">
                                ${sheet.language === 'arabic' ? 'Arabic' : 'English'}
                            </span>
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Column Mapping</h6>
                                <div class="mb-2">
                                    <label class="form-label">Product Name Column *</label>
                                    <select class="form-select" id="name-${index}" required>
                                        <option value="">Select column...</option>
                                        ${sheet.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Brand Column</label>
                                    <select class="form-select" id="brand-${index}">
                                        <option value="">Select column...</option>
                                        ${sheet.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Weight/Size Column</label>
                                    <select class="form-select" id="weight-${index}">
                                        <option value="">Select column...</option>
                                        ${sheet.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Category Column</label>
                                    <select class="form-select" id="category-${index}">
                                        <option value="">Select column...</option>
                                        ${sheet.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">Additional Columns</label>
                                    <select class="form-select" id="additional-${index}" multiple>
                                        ${sheet.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                                    </select>
                                    <div class="form-text">Hold Ctrl/Cmd to select multiple columns</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Data Preview</h6>
                                <div class="preview-table">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                ${sheet.columns.slice(0, 4).map(col => `<th>${col}</th>`).join('')}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${sheet.preview.map(row => `
                                                <tr>
                                                    ${sheet.columns.slice(0, 4).map(col => `<td>${row[col] || ''}</td>`).join('')}
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            configDiv.innerHTML = configHtml;
        }

        function saveConfiguration() {
            const sheetNames = Object.keys(sheetsInfo);

            console.log('Saving configuration...'); // Debug log
            console.log('Available sheets:', sheetNames); // Debug log

            if (sheetNames.length !== 2) {
                showAlert('Error: Expected 2 sheets, found ' + sheetNames.length);
                return;
            }

            const config = {
                sheet1: sheetNames[0],
                sheet2: sheetNames[1],
                sheet1_columns: getColumnMapping(0),
                sheet2_columns: getColumnMapping(1),
                threshold: parseFloat(document.getElementById('threshold').value),
                language: sheetsInfo[sheetNames[0]].language
            };

            console.log('Configuration:', config); // Debug log

            // Validate required fields
            if (!config.sheet1_columns.name || !config.sheet2_columns.name) {
                showAlert('Please select product name columns for both sheets.');
                return;
            }

            fetch('/configure', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentStep = 3;
                    updateStepIndicator(currentStep);
                    document.getElementById('config-section').classList.add('hidden');
                    document.getElementById('matching-section').classList.remove('hidden');
                    showAlert('Configuration saved successfully!', 'success');
                } else {
                    showAlert(data.error || 'Configuration failed.');
                }
            })
            .catch(error => {
                showAlert('Configuration failed: ' + error.message);
            });
        }

        function getColumnMapping(index) {
            console.log(`Getting column mapping for index ${index}`); // Debug log

            const nameElement = document.getElementById(`name-${index}`);
            const brandElement = document.getElementById(`brand-${index}`);
            const weightElement = document.getElementById(`weight-${index}`);
            const categoryElement = document.getElementById(`category-${index}`);
            const additionalElement = document.getElementById(`additional-${index}`);

            if (!nameElement) {
                console.error(`Name element not found for index ${index}`);
                return { name: null, brand: null, weight: null, category: null, additional: [] };
            }

            const mapping = {
                name: nameElement.value,
                brand: brandElement ? brandElement.value || null : null,
                weight: weightElement ? weightElement.value || null : null,
                category: categoryElement ? categoryElement.value || null : null,
                additional: additionalElement ? Array.from(additionalElement.selectedOptions).map(opt => opt.value) : []
            };

            console.log(`Column mapping for index ${index}:`, mapping); // Debug log
            return mapping;
        }

        function startMatching() {
            document.getElementById('matchBtn').classList.add('hidden');
            document.getElementById('matching-progress').classList.remove('hidden');

            fetch('/match', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultId = data.summary.result_id;
                    showResults(data.summary, data.preview);
                    currentStep = 4;
                    updateStepIndicator(currentStep);
                    document.getElementById('matching-section').classList.add('hidden');
                    document.getElementById('results-section').classList.remove('hidden');
                    showAlert('Matching completed successfully!', 'success');
                } else {
                    showAlert(data.error || 'Matching failed.');
                    document.getElementById('matchBtn').classList.remove('hidden');
                    document.getElementById('matching-progress').classList.add('hidden');
                }
            })
            .catch(error => {
                showAlert('Matching failed: ' + error.message);
                document.getElementById('matchBtn').classList.remove('hidden');
                document.getElementById('matching-progress').classList.add('hidden');
            });
        }

        function showResults(summary, preview) {
            const summaryDiv = document.getElementById('results-summary');
            const previewDiv = document.getElementById('results-preview');

            // Show summary
            summaryDiv.innerHTML = `
                <h6>Matching Summary</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">${summary.total_matches}</h4>
                            <small>Matched Products</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-warning">${summary.unmatched_1}</h4>
                            <small>Unmatched from Sheet 1</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info">${summary.unmatched_2}</h4>
                            <small>Unmatched from Sheet 2</small>
                        </div>
                    </div>
                </div>
            `;

            // Show preview
            let previewHtml = '';
            if (preview.matched.length > 0) {
                previewHtml += `
                    <h6>Sample Matched Products</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    ${Object.keys(preview.matched[0]).map(key => `<th>${key}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${preview.matched.slice(0, 5).map(row => `
                                    <tr>
                                        ${Object.values(row).map(val => `<td>${val || ''}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            }

            previewDiv.innerHTML = previewHtml;
        }

        function downloadResults() {
            if (!resultId) {
                showAlert('No results available for download.');
                return;
            }

            window.location.href = `/download/${resultId}`;
        }

        function startNewMatch() {
            // Clean up
            fetch('/cleanup', { method: 'GET' });
            
            // Reset UI
            currentStep = 1;
            updateStepIndicator(currentStep);
            fileId = null;
            resultId = null;
            sheetsInfo = {};
            
            document.getElementById('results-section').classList.add('hidden');
            document.getElementById('matching-section').classList.add('hidden');
            document.getElementById('config-section').classList.add('hidden');
            document.getElementById('upload-section').classList.remove('hidden');
            
            document.getElementById('fileInput').value = '';
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('alerts').innerHTML = '';
        }
    </script>
</body>
</html>
