#!/usr/bin/env python3
"""
Test script for the Product Matcher

This script tests the core functionality of the product matcher
to ensure everything is working correctly.
"""

import pandas as pd
import numpy as np
from product_matcher import ProductMatcher

def create_test_data():
    """Create sample test data"""
    
    # Sample data for company 1 (Arabic)
    company1_data = {
        'Product Name': [
            'عصير برتقال طبيعي 1 لتر',
            'شامبو للشعر الجاف 400 مل',
            'بسكويت شوكولاته 200 جرام',
            'منظف أطباق ليمون 500 مل',
            'أرز بسمتي 1 كيلو'
        ],
        'Brand': ['العصائر الطبيعية', 'شامبو بلس', 'بسكو', 'كلين', 'الأرز الذهبي'],
        'Category': ['مشروبات', 'عناية شخصية', 'حلويات', 'منظفات', 'حبوب']
    }
    
    # Sample data for company 2 (English)
    company2_data = {
        'Product Name': [
            'Natural Orange Juice 1L',
            'Dry Hair Shampoo 400ml',
            'Chocolate Biscuits 200g',
            'Lemon Dish Cleaner 500ml',
            'Basmati Rice 1kg',
            'Apple Juice 1L',
            'Hair Conditioner 300ml'
        ],
        'Brand': ['Natural Juices', 'Shampoo Plus', 'Bisco', 'Clean', 'Golden Rice', 'Fresh', 'Hair Care'],
        'Category': ['Beverages', 'Personal Care', 'Snacks', 'Cleaning', 'Grains', 'Beverages', 'Personal Care']
    }
    
    df1 = pd.DataFrame(company1_data)
    df2 = pd.DataFrame(company2_data)
    
    return df1, df2

def test_arabic_processing():
    """Test Arabic text processing"""
    print("Testing Arabic text processing...")
    
    matcher = ProductMatcher()
    
    # Test Arabic normalization
    arabic_text = "عصير برتقال طبيعي ١ لتر"
    normalized = matcher.normalize_text(arabic_text, 'arabic')
    print(f"Original: {arabic_text}")
    print(f"Normalized: {normalized}")
    
    # Test feature extraction
    features = matcher.extract_features(arabic_text, 'arabic')
    print(f"Features: {features}")
    print("✅ Arabic processing test passed\n")

def test_english_processing():
    """Test English text processing"""
    print("Testing English text processing...")
    
    matcher = ProductMatcher()
    
    # Test English normalization
    english_text = "Natural Orange Juice 1L"
    normalized = matcher.normalize_text(english_text, 'english')
    print(f"Original: {english_text}")
    print(f"Normalized: {normalized}")
    
    # Test feature extraction
    features = matcher.extract_features(english_text, 'english')
    print(f"Features: {features}")
    print("✅ English processing test passed\n")

def test_matching():
    """Test the matching functionality"""
    print("Testing product matching...")
    
    # Create test data
    df1, df2 = create_test_data()
    
    # Initialize matcher
    matcher = ProductMatcher()
    
    # Define column mappings
    df1_columns = {
        'name': 'Product Name',
        'brand': 'Brand',
        'category': 'Category'
    }
    
    df2_columns = {
        'name': 'Product Name',
        'brand': 'Brand',
        'category': 'Category'
    }
    
    # Perform matching
    results = matcher.match_products(
        df1=df1,
        df2=df2,
        df1_columns=df1_columns,
        df2_columns=df2_columns,
        threshold=50.0,
        language='arabic'  # Primary language
    )
    
    print("Matching Results:")
    print(f"- Matched products: {len(results['matched'])}")
    print(f"- Unmatched from dataset 1: {len(results['unmatched_1'])}")
    print(f"- Unmatched from dataset 2: {len(results['unmatched_2'])}")
    
    if not results['matched'].empty:
        print("\nSample matches:")
        print(results['matched'].head())
    
    print("✅ Matching test completed\n")

def test_similarity_calculation():
    """Test similarity calculation"""
    print("Testing similarity calculation...")
    
    matcher = ProductMatcher()
    
    # Test similar products
    text1 = "عصير برتقال طبيعي 1 لتر"
    text2 = "natural orange juice 1l"
    
    features1 = matcher.extract_features(text1, 'arabic')
    features2 = matcher.extract_features(text2, 'english')
    
    similarity = matcher.calculate_similarity(
        features1['clean_core'], 
        features2['clean_core'], 
        features1, 
        features2
    )
    
    print(f"Text 1: {text1}")
    print(f"Text 2: {text2}")
    print(f"Similarity Score: {similarity:.2f}")
    print("✅ Similarity calculation test passed\n")

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 PRODUCT MATCHER TEST SUITE")
    print("=" * 60)
    print()
    
    try:
        # Test individual components
        test_arabic_processing()
        test_english_processing()
        test_similarity_calculation()
        test_matching()
        
        print("=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("🎉 Product Matcher is working correctly!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        print("Please check the error and try again.")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
