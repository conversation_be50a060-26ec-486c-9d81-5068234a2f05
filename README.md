# Product Matcher Web Application

A web application for matching products between two companies using advanced text similarity algorithms and machine learning techniques. This application can handle both Arabic and English text and provides an intuitive interface for uploading Excel files, configuring column mappings, and downloading matching results.

## Features

- **Multi-language Support**: Automatic detection and processing of Arabic and English text
- **Flexible Column Mapping**: Users can map any columns from their Excel sheets to product attributes
- **Advanced Matching Algorithm**: Uses TF-IDF vectorization, fuzzy string matching, and attribute comparison
- **Interactive Web Interface**: Step-by-step process with real-time feedback
- **Export Results**: Download matching results as Excel files with multiple sheets

## Requirements

- Python 3.8 or higher
- Excel files (.xlsx or .xls) with exactly 2 sheets

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Create necessary directories** (if not already present):
   ```bash
   mkdir uploads results
   ```

## Usage

1. **Start the application**:
   ```bash
   python app.py
   ```

2. **Open your web browser** and navigate to:
   ```
   http://localhost:5000
   ```

3. **Follow the 4-step process**:

   ### Step 1: Upload Excel File
   - Select an Excel file with exactly 2 sheets
   - Each sheet should represent products from a different company
   - The application will automatically analyze the file and detect the language

   ### Step 2: Configure Column Mapping
   - For each sheet, map the columns to product attributes:
     - **Product Name** (Required): The main product name/description
     - **Brand** (Optional): Brand or manufacturer name
     - **Weight/Size** (Optional): Product weight, volume, or size information
     - **Category** (Optional): Product category or classification
     - **Additional Columns** (Optional): Any other columns to include in results
   - Set the matching threshold (0-100, default: 60)

   ### Step 3: Start Matching
   - Click "Start Matching" to begin the process
   - The algorithm will compare products and find matches based on:
     - Text similarity (using multiple fuzzy matching techniques)
     - Brand consistency
     - Attribute similarity (weight, volume, pack count)
     - Category alignment

   ### Step 4: Download Results
   - View the matching summary and preview
   - Download the complete results as an Excel file with three sheets:
     - **Matched Products**: Successfully matched products with similarity scores
     - **Only in Sheet 1**: Products that couldn't be matched from the first sheet
     - **Only in Sheet 2**: Products that couldn't be matched from the second sheet

## Algorithm Details

The matching algorithm uses several techniques:

1. **Text Normalization**:
   - Arabic: Removes diacritics, normalizes characters, handles different spellings
   - English: Standardizes punctuation and spacing

2. **Feature Extraction**:
   - Extracts weight, volume, and pack count information
   - Removes noise words and creates clean text for matching

3. **Similarity Calculation**:
   - TF-IDF vectorization for initial candidate generation
   - Multiple fuzzy string matching methods (token sort, token set, partial ratio)
   - Attribute-based bonuses and penalties
   - Brand consistency checks

4. **Matching Process**:
   - Uses nearest neighbor search for efficiency
   - Applies configurable similarity threshold
   - Prevents duplicate matches (one-to-one mapping)

## File Structure

```
product-matcher/
├── app.py                 # Main Flask application
├── product_matcher.py     # Core matching algorithm
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── templates/
│   └── index.html        # Web interface template
├── static/
│   └── style.css         # Custom CSS styles
├── uploads/              # Temporary file storage
└── results/              # Generated result files
```

## Configuration

You can modify the following parameters in the web interface:

- **Matching Threshold**: Minimum similarity score (0-100) to accept a match
- **Column Mappings**: Which columns to use for different product attributes
- **Language Detection**: Automatically detected but affects text processing

## Troubleshooting

### Common Issues

1. **File Upload Fails**:
   - Ensure the file is a valid Excel format (.xlsx or .xls)
   - Check that the file has exactly 2 sheets
   - File size should be under 16MB

2. **No Matches Found**:
   - Try lowering the matching threshold
   - Ensure product name columns are correctly mapped
   - Check that the data contains meaningful product descriptions

3. **Poor Matching Quality**:
   - Verify column mappings are correct
   - Consider including brand and weight/size columns
   - Increase the threshold for stricter matching

4. **Language Detection Issues**:
   - The system automatically detects Arabic vs English
   - Mixed-language datasets may need manual review

### Performance Notes

- Processing time depends on dataset size and complexity
- Large datasets (>1000 products per sheet) may take several minutes
- The application uses efficient algorithms but complex matching is computationally intensive

## Technical Details

### Dependencies

- **Flask**: Web framework
- **pandas**: Data manipulation
- **scikit-learn**: TF-IDF vectorization and nearest neighbors
- **rapidfuzz**: Fast fuzzy string matching
- **langdetect**: Language detection
- **openpyxl**: Excel file handling

### Security Considerations

- Files are temporarily stored and automatically cleaned up
- Session-based file management prevents unauthorized access
- No persistent data storage (files are deleted after processing)

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify your Excel file format and structure
3. Ensure all required columns are properly mapped

## License

This project is provided as-is for educational and commercial use.
