import re
import math
import unicodedata
from typing import Optional, Tuple, Dict, Any, List
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.neighbors import NearestNeighbors
from rapidfuzz import fuzz
from tqdm.auto import tqdm

class ProductMatcher:
    """
    Generalized product matching system that can work with any two datasets
    and user-defined column mappings.
    """
    
    def __init__(self):
        # Arabic normalization patterns
        self.arabic_diacritics = re.compile(r"[\u0610-\u061A\u064B-\u065F\u06D6-\u06ED]")
        self.tatweel = "\u0640"
        self.arabic_digits = str.maketrans("٠١٢٣٤٥٦٧٨٩", "0123456789")
        self.persian_digits = str.maketrans("۰۱۲۳۴۵۶۷۸۹", "0123456789")
        self.punct_pattern = re.compile(r"[^\w\s]+", flags=re.UNICODE)
        
        # Unit extraction patterns
        self.unit_patterns = [
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:كيلو?جرام|كجم|كلغ|كغ|kg)\b"), 1000.0, "g"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:جرام|غ|جم|g)\b"), 1.0, "g"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:ليتر|لتر|ل|ltr|liter|litre|lt|l)\b"), 1000.0, "ml"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:مل|ml)\b"), 1.0, "ml"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:cl)\b"), 10.0, "ml"),
        ]
        
        # English unit patterns
        self.english_unit_patterns = [
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:kilogram|kilograms|kg)\b", re.IGNORECASE), 1000.0, "g"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:gram|grams|g)\b", re.IGNORECASE), 1.0, "g"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:liter|liters|litre|litres|l)\b", re.IGNORECASE), 1000.0, "ml"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:milliliter|milliliters|ml)\b", re.IGNORECASE), 1.0, "ml"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:ounce|ounces|oz)\b", re.IGNORECASE), 28.35, "g"),
            (re.compile(r"(\d+(?:[.,]\d+)?)\s*(?:pound|pounds|lb|lbs)\b", re.IGNORECASE), 453.6, "g"),
        ]
        
        # Pack count patterns
        self.pack_patterns = [
            re.compile(r"\b(\d+)\s*x\s*(\d+(?:[.,]\d+)?)\s*(?:مل|ml|جرام|غ|جم|g|kg|كجم|كيلو?جرام|ل|لتر|ليتر|l|ltr)?\b"),
            re.compile(r"\b(\d+)\s*x\b"),
            re.compile(r"\bpack\s*of\s*(\d+)\b", re.IGNORECASE),
            re.compile(r"\b(\d+)\s*pack\b", re.IGNORECASE),
        ]
        
        # Noise words for different languages
        self.arabic_noise_words = {
            "عرض","عرض خاص","مجانا","هديه","هدية","مجاني","جديد","خصم","بوكس","علبه","علبة","علب",
            "عبوه","عبوة","عبوات","سعر","مسعر","الجديد","المحسن","حجم","كبير","صغير","متوسط",
            "اكسترا","زيادة","كو","شركة","شركه","براند","ماركه","ماركة","اصلي","اصلية","اصليه"
        }
        
        self.english_noise_words = {
            "new", "fresh", "special", "offer", "sale", "discount", "pack", "box", "case", "bottle",
            "can", "jar", "bag", "original", "classic", "premium", "deluxe", "extra", "super",
            "large", "small", "medium", "mini", "jumbo", "family", "size", "brand", "company", "co"
        }

    def normalize_text(self, text: str, language: str = 'english') -> str:
        """Normalize text based on language"""
        if not isinstance(text, str):
            return ""
        
        text = text.strip().lower()
        
        if language == 'arabic':
            return self._normalize_arabic(text)
        else:
            return self._normalize_english(text)
    
    def _normalize_arabic(self, text: str) -> str:
        """Arabic-specific normalization"""
        text = unicodedata.normalize("NFKC", text)
        text = self.arabic_diacritics.sub("", text)
        text = text.replace(self.tatweel, "")
        text = re.sub("[إأآٱا]", "ا", text)
        text = text.replace("ة", "ه")
        text = text.replace("ى", "ي").replace("ئ", "ي").replace("ؤ", "و")
        text = text.replace("×", "x").replace("✕", "x").replace("*", "x")
        text = text.translate(self.arabic_digits).translate(self.persian_digits)
        text = re.sub(r"[/|+_,\-]+", " ", text)
        text = self.punct_pattern.sub(" ", text)
        text = re.sub(r"\s+", " ", text).strip()
        return text
    
    def _normalize_english(self, text: str) -> str:
        """English-specific normalization"""
        text = re.sub(r"[^\w\s]", " ", text)
        text = re.sub(r"\s+", " ", text).strip()
        return text

    def extract_features(self, text: str, language: str = 'english') -> Dict[str, Any]:
        """Extract features from product text"""
        if not text:
            return {
                "clean_text": "",
                "clean_core": "",
                "net_weight_g": np.nan,
                "volume_ml": np.nan,
                "pack_count": np.nan
            }
        
        clean_text = self.normalize_text(text, language)
        
        # Extract units
        weight_g, volume_ml = self._extract_units(clean_text, language)
        
        # Extract pack count
        pack_count = self._extract_pack_count(clean_text)
        
        # Create clean core (remove numbers and units for better text matching)
        clean_core = self._create_clean_core(clean_text, language)
        
        return {
            "clean_text": clean_text,
            "clean_core": clean_core,
            "net_weight_g": weight_g,
            "volume_ml": volume_ml,
            "pack_count": pack_count
        }
    
    def _extract_units(self, text: str, language: str) -> Tuple[float, float]:
        """Extract weight and volume units"""
        best_g = np.nan
        best_ml = np.nan
        
        patterns = self.unit_patterns if language == 'arabic' else self.english_unit_patterns
        
        for pat, factor, target in patterns:
            for m in pat.finditer(text):
                try:
                    value = float(m.group(1).replace(",", ".")) * factor
                    if target == "g":
                        if math.isnan(best_g) or value > best_g:
                            best_g = value
                    else:
                        if math.isnan(best_ml) or value > best_ml:
                            best_ml = value
                except ValueError:
                    continue
        
        return best_g, best_ml
    
    def _extract_pack_count(self, text: str) -> float:
        """Extract pack count from text"""
        for pat in self.pack_patterns:
            m = pat.search(text)
            if m:
                try:
                    if "x" in pat.pattern and len(m.groups()) >= 2:
                        return float(m.group(1))
                    else:
                        num_str = next((g for g in m.groups() if g is not None), None)
                        if num_str:
                            return float(num_str)
                except ValueError:
                    continue
        return np.nan
    
    def _create_clean_core(self, text: str, language: str) -> str:
        """Create clean core text for matching"""
        # Remove numbers and units
        core = re.sub(r"\b(\d+(?:[.,]\d+)?)\b", " ", text)
        core = re.sub(r"\b(مل|ml|جرام|جم|غ|g|kg|كجم|كيلو?جرام|ل|لتر|ليتر|ltr|l|gram|grams|kilogram|kg|liter|liters)\b", " ", core)
        core = re.sub(r"\bx\b", " ", core)
        
        # Remove noise words
        noise_words = self.arabic_noise_words if language == 'arabic' else self.english_noise_words
        tokens = [t for t in core.split() if t not in noise_words and len(t) > 1]
        
        core = " ".join(tokens)
        core = re.sub(r"\s+", " ", core).strip()
        return core

    def normalize_brand(self, text: str, language: str = 'english') -> str:
        """Normalize brand names"""
        if not isinstance(text, str) or not text.strip():
            return ""
        
        normalized = self.normalize_text(text, language)
        
        if language == 'arabic':
            # Remove Arabic company suffixes
            normalized = re.sub(r"\b(شركه|شركة|كو|المحدوده|محدوده|المصريه|المصرية|ايجيبت|جروب|ماركه|براند|صناعات)\b", " ", normalized)
        else:
            # Remove English company suffixes
            normalized = re.sub(r"\b(company|corp|corporation|inc|incorporated|ltd|limited|llc|co|group|brand)\b", " ", normalized)
        
        normalized = re.sub(r"\s+", " ", normalized).strip()
        return normalized

    def calculate_similarity(self, text1: str, text2: str, features1: Dict, features2: Dict) -> float:
        """Calculate similarity score between two products"""
        # Text similarity using multiple fuzzy matching methods
        tsr = fuzz.token_sort_ratio(text1, text2) / 100.0
        tset = fuzz.token_set_ratio(text1, text2) / 100.0
        pr = fuzz.partial_ratio(text1, text2) / 100.0
        
        # Weighted combination of fuzzy scores
        text_score = 0.5 * tset + 0.3 * tsr + 0.2 * pr
        
        # Attribute similarity bonus
        attr_bonus = self._calculate_attribute_bonus(features1, features2)
        
        # Final score
        total_score = (text_score * 100.0) + attr_bonus
        
        return total_score
    
    def _calculate_attribute_bonus(self, f1: Dict, f2: Dict) -> float:
        """Calculate bonus/penalty based on attribute similarity"""
        bonus = 0.0
        
        def rel_diff(x, y):
            if pd.isna(x) or pd.isna(y):
                return None
            denom = max(abs(x), abs(y), 1.0)
            return abs(x - y) / denom
        
        # Weight similarity
        weight_diff = rel_diff(f1.get("net_weight_g"), f2.get("net_weight_g"))
        if weight_diff is not None:
            if weight_diff <= 0.05:  # 5% tolerance
                bonus += 15.0
            elif weight_diff <= 0.15:  # 15% tolerance
                bonus += 8.0
            elif weight_diff > 0.3:  # 30% difference
                bonus -= 20.0
        
        # Volume similarity
        volume_diff = rel_diff(f1.get("volume_ml"), f2.get("volume_ml"))
        if volume_diff is not None:
            if volume_diff <= 0.05:
                bonus += 15.0
            elif volume_diff <= 0.15:
                bonus += 8.0
            elif volume_diff > 0.3:
                bonus -= 20.0
        
        # Pack count similarity
        pack_diff = rel_diff(f1.get("pack_count"), f2.get("pack_count"))
        if pack_diff is not None:
            if pack_diff == 0:
                bonus += 10.0
            elif pack_diff > 0:
                bonus -= 5.0
        
        return bonus

    def match_products(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                      df1_columns: Dict, df2_columns: Dict,
                      threshold: float = 60.0, language: str = 'english') -> Dict:
        """
        Main matching function
        
        Args:
            df1, df2: DataFrames to match
            df1_columns, df2_columns: Column mappings with keys: name, brand, weight, category, additional
            threshold: Minimum score for accepting a match
            language: 'arabic' or 'english'
        
        Returns:
            Dict with matched, unmatched_1, unmatched_2 DataFrames
        """
        
        # Prepare datasets
        df1_prep = self._prepare_dataset(df1, df1_columns, language, "Dataset1")
        df2_prep = self._prepare_dataset(df2, df2_columns, language, "Dataset2")
        
        # Perform matching
        matched_pairs = []
        matched_indices_1 = set()
        matched_indices_2 = set()
        
        # Use TF-IDF for initial candidate generation
        vectorizer = TfidfVectorizer(
            analyzer="word", 
            ngram_range=(1, 2), 
            min_df=1, 
            max_df=0.95, 
            lowercase=False
        )
        
        try:
            # Fit on combined corpus
            all_texts = df1_prep["clean_core"].tolist() + df2_prep["clean_core"].tolist()
            vectorizer.fit(all_texts)
            
            df2_matrix = vectorizer.transform(df2_prep["clean_core"])
            nn = NearestNeighbors(n_neighbors=min(10, len(df2_prep)), metric="cosine")
            nn.fit(df2_matrix)
            
            # Match each item in df1
            for idx1, row1 in tqdm(df1_prep.iterrows(), total=len(df1_prep), desc="Matching products"):
                if not row1["clean_core"].strip():
                    continue
                
                # Get candidates using TF-IDF
                query_vec = vectorizer.transform([row1["clean_core"]])
                distances, indices = nn.kneighbors(query_vec)
                
                best_score = -1
                best_idx2 = None
                
                for i, idx2_pos in enumerate(indices[0]):
                    idx2 = df2_prep.index[idx2_pos]
                    if idx2 in matched_indices_2:
                        continue
                    
                    row2 = df2_prep.loc[idx2]
                    
                    # Skip if brand mismatch (when both have brands)
                    if (row1["brand_clean"] and row2["brand_clean"] and 
                        row1["brand_clean"] != row2["brand_clean"]):
                        continue
                    
                    # Calculate similarity
                    score = self.calculate_similarity(
                        row1["clean_core"], row2["clean_core"],
                        row1.to_dict(), row2.to_dict()
                    )
                    
                    if score > best_score:
                        best_score = score
                        best_idx2 = idx2
                
                # Accept match if above threshold
                if best_score >= threshold and best_idx2 is not None:
                    row2 = df2_prep.loc[best_idx2]
                    matched_pairs.append({
                        "Dataset1_Name": row1["original_name"],
                        "Dataset1_Brand": row1.get("brand_original", ""),
                        "Dataset1_Weight": row1.get("weight_original", ""),
                        "Dataset1_Category": row1.get("category_original", ""),
                        "Dataset2_Name": row2["original_name"],
                        "Dataset2_Brand": row2.get("brand_original", ""),
                        "Dataset2_Weight": row2.get("weight_original", ""),
                        "Dataset2_Category": row2.get("category_original", ""),
                        "Match_Score": f"{best_score:.1f}%"
                    })
                    matched_indices_1.add(idx1)
                    matched_indices_2.add(best_idx2)
        
        except Exception as e:
            print(f"Error during matching: {e}")
            # Fallback to simple fuzzy matching
            matched_pairs = self._fallback_matching(df1_prep, df2_prep, threshold)
            matched_indices_1 = set()
            matched_indices_2 = set()
        
        # Create result DataFrames
        matched_df = pd.DataFrame(matched_pairs) if matched_pairs else pd.DataFrame()
        
        unmatched_1 = df1_prep[~df1_prep.index.isin(matched_indices_1)].copy()
        unmatched_2 = df2_prep[~df2_prep.index.isin(matched_indices_2)].copy()
        
        # Clean up unmatched DataFrames
        unmatched_1 = self._clean_unmatched_df(unmatched_1, "Dataset1")
        unmatched_2 = self._clean_unmatched_df(unmatched_2, "Dataset2")
        
        return {
            "matched": matched_df,
            "unmatched_1": unmatched_1,
            "unmatched_2": unmatched_2
        }
    
    def _prepare_dataset(self, df: pd.DataFrame, columns: Dict, language: str, prefix: str) -> pd.DataFrame:
        """Prepare dataset for matching"""
        result = df.copy()
        
        # Extract features from name column
        name_col = columns["name"]
        result["original_name"] = result[name_col].fillna("").astype(str)
        
        features_list = []
        for _, row in result.iterrows():
            features = self.extract_features(row[name_col], language)
            features_list.append(features)
        
        features_df = pd.DataFrame(features_list)
        result = pd.concat([result, features_df], axis=1)
        
        # Handle other columns
        if columns.get("brand"):
            result["brand_original"] = result[columns["brand"]].fillna("").astype(str)
            result["brand_clean"] = result["brand_original"].apply(lambda x: self.normalize_brand(x, language))
        else:
            result["brand_original"] = ""
            result["brand_clean"] = ""
        
        if columns.get("weight"):
            result["weight_original"] = result[columns["weight"]].fillna("").astype(str)
        
        if columns.get("category"):
            result["category_original"] = result[columns["category"]].fillna("").astype(str)
        
        return result
    
    def _fallback_matching(self, df1: pd.DataFrame, df2: pd.DataFrame, threshold: float) -> List[Dict]:
        """Fallback matching using simple fuzzy matching"""
        matched_pairs = []
        
        for _, row1 in df1.iterrows():
            best_score = -1
            best_row2 = None
            
            for _, row2 in df2.iterrows():
                score = fuzz.token_sort_ratio(row1["clean_core"], row2["clean_core"])
                if score > best_score:
                    best_score = score
                    best_row2 = row2
            
            if best_score >= threshold:
                matched_pairs.append({
                    "Dataset1_Name": row1["original_name"],
                    "Dataset1_Brand": row1.get("brand_original", ""),
                    "Dataset2_Name": best_row2["original_name"],
                    "Dataset2_Brand": best_row2.get("brand_original", ""),
                    "Match_Score": f"{best_score:.1f}%"
                })
        
        return matched_pairs
    
    def _clean_unmatched_df(self, df: pd.DataFrame, prefix: str) -> pd.DataFrame:
        """Clean unmatched DataFrame for output"""
        if df.empty:
            return df
        
        columns_to_keep = ["original_name"]
        rename_map = {"original_name": f"{prefix}_Name"}
        
        if "brand_original" in df.columns:
            columns_to_keep.append("brand_original")
            rename_map["brand_original"] = f"{prefix}_Brand"
        
        if "weight_original" in df.columns:
            columns_to_keep.append("weight_original")
            rename_map["weight_original"] = f"{prefix}_Weight"
        
        if "category_original" in df.columns:
            columns_to_keep.append("category_original")
            rename_map["category_original"] = f"{prefix}_Category"
        
        # Keep only relevant columns
        available_columns = [col for col in columns_to_keep if col in df.columns]
        result = df[available_columns].copy()
        result = result.rename(columns=rename_map)
        
        return result.reset_index(drop=True)
