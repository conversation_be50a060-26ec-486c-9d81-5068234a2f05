from flask import Flask, render_template, request, jsonify, send_file, session
import pandas as pd
import os
import uuid
from werkzeug.utils import secure_filename
import tempfile
import json
from datetime import datetime
import langdetect
from product_matcher import ProductMatcher

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure upload folder
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULTS_FOLDER'] = RESULTS_FOLDER

ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def detect_language(text_samples):
    """Detect if the text is primarily Arabic or English"""
    if not text_samples:
        return 'english'
    
    # Combine samples and clean
    combined_text = ' '.join(str(sample) for sample in text_samples if pd.notna(sample))
    if not combined_text.strip():
        return 'english'
    
    try:
        # Use langdetect for basic detection
        detected = langdetect.detect(combined_text)
        if detected == 'ar':
            return 'arabic'
        else:
            return 'english'
    except:
        # Fallback: check for Arabic characters
        arabic_chars = sum(1 for char in combined_text if '\u0600' <= char <= '\u06FF')
        total_chars = len([c for c in combined_text if c.isalpha()])
        
        if total_chars > 0 and arabic_chars / total_chars > 0.3:
            return 'arabic'
        else:
            return 'english'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{file_id}_{filename}")
        file.save(file_path)
        
        try:
            # Read Excel file and get sheet information
            excel_file = pd.ExcelFile(file_path)
            sheets_info = {}
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=5)  # Read first 5 rows for preview

                # Get column information
                columns = df.columns.tolist()

                # Sample data for language detection
                sample_data = []
                for col in columns[:3]:  # Check first 3 columns
                    sample_data.extend(df[col].dropna().head(3).tolist())

                language = detect_language(sample_data)

                # Convert preview data and handle NaN values
                preview_data = df.head(3).fillna('').to_dict('records')

                sheets_info[sheet_name] = {
                    'columns': columns,
                    'row_count': len(df),
                    'language': language,
                    'preview': preview_data
                }
            
            # Store file info in session
            session['file_id'] = file_id
            session['file_path'] = file_path
            session['sheets_info'] = sheets_info
            
            return jsonify({
                'success': True,
                'file_id': file_id,
                'sheets': sheets_info
            })
            
        except Exception as e:
            # Clean up file on error
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({'error': f'Error reading Excel file: {str(e)}'}), 400
    
    return jsonify({'error': 'Invalid file type. Please upload an Excel file (.xlsx or .xls)'}), 400

@app.route('/configure', methods=['POST'])
def configure_matching():
    try:
        data = request.get_json()
        
        # Validate required data
        required_fields = ['sheet1', 'sheet2', 'sheet1_columns', 'sheet2_columns']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Store configuration in session
        session['matching_config'] = data
        
        return jsonify({'success': True, 'message': 'Configuration saved successfully'})
        
    except Exception as e:
        return jsonify({'error': f'Error saving configuration: {str(e)}'}), 400

@app.route('/match', methods=['POST'])
def start_matching():
    try:
        if 'file_path' not in session or 'matching_config' not in session:
            return jsonify({'error': 'No file uploaded or configuration missing'}), 400
        
        file_path = session['file_path']
        config = session['matching_config']
        
        # Load the datasets
        df1 = pd.read_excel(file_path, sheet_name=config['sheet1'])
        df2 = pd.read_excel(file_path, sheet_name=config['sheet2'])
        
        # Initialize the product matcher
        matcher = ProductMatcher()
        
        # Perform matching
        results = matcher.match_products(
            df1=df1,
            df2=df2,
            df1_columns=config['sheet1_columns'],
            df2_columns=config['sheet2_columns'],
            threshold=config.get('threshold', 60.0),
            language=config.get('language', 'english')
        )
        
        # Save results
        result_id = str(uuid.uuid4())
        result_path = os.path.join(app.config['RESULTS_FOLDER'], f"results_{result_id}.xlsx")
        
        with pd.ExcelWriter(result_path, engine='openpyxl') as writer:
            if results['matched'] is not None and not results['matched'].empty:
                results['matched'].to_excel(writer, index=False, sheet_name='Matched Products')
            
            if results['unmatched_1'] is not None and not results['unmatched_1'].empty:
                results['unmatched_1'].to_excel(writer, index=False, sheet_name=f'Only in {config["sheet1"]}')
            
            if results['unmatched_2'] is not None and not results['unmatched_2'].empty:
                results['unmatched_2'].to_excel(writer, index=False, sheet_name=f'Only in {config["sheet2"]}')
        
        # Store result info in session
        session['result_id'] = result_id
        session['result_path'] = result_path
        
        # Prepare summary for display
        summary = {
            'total_matches': len(results['matched']) if results['matched'] is not None else 0,
            'unmatched_1': len(results['unmatched_1']) if results['unmatched_1'] is not None else 0,
            'unmatched_2': len(results['unmatched_2']) if results['unmatched_2'] is not None else 0,
            'result_id': result_id
        }
        
        return jsonify({
            'success': True,
            'summary': summary,
            'preview': {
                'matched': results['matched'].head(10).fillna('').to_dict('records') if results['matched'] is not None and not results['matched'].empty else [],
                'unmatched_1': results['unmatched_1'].head(5).fillna('').to_dict('records') if results['unmatched_1'] is not None and not results['unmatched_1'].empty else [],
                'unmatched_2': results['unmatched_2'].head(5).fillna('').to_dict('records') if results['unmatched_2'] is not None and not results['unmatched_2'].empty else []
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'Error during matching: {str(e)}'}), 500

@app.route('/download/<result_id>')
def download_results(result_id):
    try:
        if 'result_id' not in session or session['result_id'] != result_id:
            return jsonify({'error': 'Invalid result ID'}), 400
        
        result_path = session.get('result_path')
        if not result_path or not os.path.exists(result_path):
            return jsonify({'error': 'Result file not found'}), 404
        
        return send_file(
            result_path,
            as_attachment=True,
            download_name=f'product_matching_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        return jsonify({'error': f'Error downloading file: {str(e)}'}), 500

@app.route('/cleanup')
def cleanup():
    """Clean up uploaded files and results"""
    try:
        # Clean up session files
        if 'file_path' in session:
            file_path = session['file_path']
            if os.path.exists(file_path):
                os.remove(file_path)
        
        if 'result_path' in session:
            result_path = session['result_path']
            if os.path.exists(result_path):
                os.remove(result_path)
        
        # Clear session
        session.clear()
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': f'Error during cleanup: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
