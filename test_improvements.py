#!/usr/bin/env python3
"""
Test script to verify the improvements to the matching algorithm
"""

import pandas as pd
import numpy as np
from Talabeyah_Product_Matcher import match_products_brand_blocked, build_features, hard_semantic_filter, compare_attributes

def test_weight_mismatch():
    """Test that 1kg products don't match with 350g products"""
    print("🧪 Testing weight mismatch prevention...")
    
    # Create test data with weight mismatches
    talab_data = {
        'Name': [
            'مكرونه بساطه مرمريه 1 ك',
            'مكرونه بساطه هلاليه 1 ك',
            'مكرونه بساطه خواتم 1 ك'
        ],
        'Brand Name': ['بساطة', 'بساطة', 'بساطة']
    }
    
    maxab_data = {
        'product_name': [
            'مكرونة بساطة مرمرية - 350 جم',
            'مكرونة بساطة هلالية - 350 جم', 
            'مكرونة بساطة خواتم - 350 جم'
        ],
        'brand_name': ['بساطة', 'بساطة', 'بساطة']
    }
    
    talab_df = pd.DataFrame(talab_data)
    maxab_df = pd.DataFrame(maxab_data)
    
    # Test with stricter threshold
    matched_df, left_only_df, right_only_df = match_products_brand_blocked(
        talab_df, maxab_df, threshold=70.0
    )
    
    print(f"Matches found: {len(matched_df) if matched_df is not None else 0}")
    if matched_df is not None and not matched_df.empty:
        print("❌ FAILED: 1kg products matched with 350g products")
        print(matched_df[['Talabeyah Product', 'Maxab Product', 'Match Score']])
    else:
        print("✅ PASSED: No incorrect weight matches")
    print()

def test_flavor_conflict():
    """Test that different flavors don't match"""
    print("🧪 Testing flavor conflict prevention...")
    
    # Create test data with flavor conflicts
    talab_data = {
        'Name': [
            'بيج كولا يوسفي 200 مل',
            'بيج كولا ليمون 200 مل',
            'بيج كولا ميكس توت 200 مل'
        ],
        'Brand Name': ['بيج', 'بيج', 'بيج']
    }
    
    maxab_data = {
        'product_name': [
            'بيج كولا - 200 مل'  # Generic, no flavor specified
        ],
        'brand_name': ['بيج']
    }
    
    talab_df = pd.DataFrame(talab_data)
    maxab_df = pd.DataFrame(maxab_data)
    
    matched_df, left_only_df, right_only_df = match_products_brand_blocked(
        talab_df, maxab_df, threshold=60.0
    )
    
    print(f"Matches found: {len(matched_df) if matched_df is not None else 0}")
    if matched_df is not None and len(matched_df) > 1:
        print("❌ FAILED: Multiple flavors matched to same generic product")
        print(matched_df[['Talabeyah Product', 'Maxab Product', 'Match Score']])
    else:
        print("✅ PASSED: Flavor conflicts prevented or limited to one match")
    print()

def test_quantity_mismatch():
    """Test that different quantities don't match"""
    print("🧪 Testing quantity mismatch prevention...")
    
    # Create test data with quantity mismatches
    talab_data = {
        'Name': [
            'ماجي مرقه دجاج 12 مكعب',
            'حفاضات بي بم بانتس مقاس 4 - حفاضه32X'
        ],
        'Brand Name': ['ماجي', 'بي بم']
    }
    
    maxab_data = {
        'product_name': [
            'ماجي مرقة دجاج أقتصادي 8 مكعب- 72 جم',
            'حفاضات بى بم بانتس مقاس 4 - 80 حفاضة'
        ],
        'brand_name': ['ماجي', 'بي بم']
    }
    
    talab_df = pd.DataFrame(talab_data)
    maxab_df = pd.DataFrame(maxab_data)
    
    matched_df, left_only_df, right_only_df = match_products_brand_blocked(
        talab_df, maxab_df, threshold=60.0
    )
    
    print(f"Matches found: {len(matched_df) if matched_df is not None else 0}")
    if matched_df is not None and not matched_df.empty:
        print("❌ FAILED: Different quantities matched")
        print(matched_df[['Talabeyah Product', 'Maxab Product', 'Match Score']])
    else:
        print("✅ PASSED: Quantity mismatches prevented")
    print()

def test_duplicate_prevention():
    """Test that one product doesn't match multiple times"""
    print("🧪 Testing duplicate match prevention...")
    
    # Create test data with potential duplicates
    talab_data = {
        'Name': [
            'مربي حلواني اخوان تين 750 جم',
            'مربي حلواني اخوان تين 750 جم عرض 10%'
        ],
        'Brand Name': ['حلواني اخوان', 'حلواني اخوان']
    }
    
    maxab_data = {
        'product_name': [
            'حلوانى مربى تين- 750 جم'
        ],
        'brand_name': ['حلواني']
    }
    
    talab_df = pd.DataFrame(talab_data)
    maxab_df = pd.DataFrame(maxab_data)
    
    matched_df, left_only_df, right_only_df = match_products_brand_blocked(
        talab_df, maxab_df, threshold=60.0
    )
    
    print(f"Matches found: {len(matched_df) if matched_df is not None else 0}")
    if matched_df is not None and len(matched_df) > 1:
        print("❌ FAILED: Same product matched multiple times")
        print(matched_df[['Talabeyah Product', 'Maxab Product', 'Match Score']])
    elif matched_df is not None and len(matched_df) == 1:
        print("✅ PASSED: Only one match kept (highest score)")
        print(matched_df[['Talabeyah Product', 'Maxab Product', 'Match Score']])
    else:
        print("✅ PASSED: No matches (acceptable)")
    print()

def test_attribute_comparison():
    """Test the improved attribute comparison function"""
    print("🧪 Testing improved attribute comparison...")
    
    # Test weight mismatch
    a = build_features("مكرونه بساطه مرمريه 1 ك")
    b = build_features("مكرونة بساطة مرمرية - 350 جم")
    
    bonus, reason = compare_attributes(a, b)
    print(f"1kg vs 350g: bonus={bonus:.1f}, reason={reason}")
    
    if bonus < -30:
        print("✅ PASSED: Large weight difference rejected")
    else:
        print("❌ FAILED: Large weight difference not rejected")
    
    # Test exact match
    a = build_features("مربي حلواني اخوان تين 750 جم")
    b = build_features("حلوانى مربى تين- 750 جم")
    
    bonus, reason = compare_attributes(a, b)
    print(f"750g vs 750g: bonus={bonus:.1f}, reason={reason}")
    
    if bonus > 5:
        print("✅ PASSED: Exact weight match gets bonus")
    else:
        print("❌ FAILED: Exact weight match should get bonus")
    print()

def main():
    """Run all improvement tests"""
    print("=" * 60)
    print("🔧 TESTING MATCHING ALGORITHM IMPROVEMENTS")
    print("=" * 60)
    print()
    
    try:
        test_weight_mismatch()
        test_flavor_conflict()
        test_quantity_mismatch()
        test_duplicate_prevention()
        test_attribute_comparison()
        
        print("=" * 60)
        print("✅ IMPROVEMENT TESTS COMPLETED!")
        print("Check the results above to see which improvements are working.")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
